{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/login", "destination": "/auth/login", "statusCode": 308, "regex": "^(?!/_next)/login(?:/)?$"}, {"source": "/signup", "destination": "/auth/signup", "statusCode": 308, "regex": "^(?!/_next)/signup(?:/)?$"}], "headers": [], "dynamicRoutes": [{"page": "/api/applications/[id]", "regex": "^/api/applications/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/applications/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/bookmarks/toggle/[jobId]", "regex": "^/api/bookmarks/toggle/([^/]+?)(?:/)?$", "routeKeys": {"nxtPjobId": "nxtPjobId"}, "namedRegex": "^/api/bookmarks/toggle/(?<nxtPjobId>[^/]+?)(?:/)?$"}, {"page": "/api/jobs/[id]", "regex": "^/api/jobs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/jobs/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/jobs/[id]", "regex": "^/jobs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/jobs/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/employee/dashboard", "regex": "^/employee/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/employee/dashboard(?:/)?$"}, {"page": "/employer/dashboard", "regex": "^/employer/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/employer/dashboard(?:/)?$"}, {"page": "/employer/jobs/new", "regex": "^/employer/jobs/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/employer/jobs/new(?:/)?$"}, {"page": "/jobs", "regex": "^/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/jobs(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}