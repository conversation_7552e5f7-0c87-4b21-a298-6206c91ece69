{"/_not-found/page": "app/_not-found/page.js", "/api/applications/[id]/route": "app/api/applications/[id]/route.js", "/api/applications/received/route": "app/api/applications/received/route.js", "/api/applications/route": "app/api/applications/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/bookmarks/toggle/[jobId]/route": "app/api/bookmarks/toggle/[jobId]/route.js", "/api/bookmarks/route": "app/api/bookmarks/route.js", "/api/jobs/my-jobs/route": "app/api/jobs/my-jobs/route.js", "/api/jobs/[id]/route": "app/api/jobs/[id]/route.js", "/api/user/notifications/route": "app/api/user/notifications/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/employee/dashboard/page": "app/employee/dashboard/page.js", "/employer/dashboard/page": "app/employer/dashboard/page.js", "/employer/jobs/new/page": "app/employer/jobs/new/page.js", "/jobs/[id]/page": "app/jobs/[id]/page.js", "/auth/login/page": "app/auth/login/page.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/profile/page": "app/profile/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/jobs/page": "app/jobs/page.js", "/page": "app/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js"}