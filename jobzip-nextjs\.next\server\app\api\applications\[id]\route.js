"use strict";(()=>{var e={};e.id=689,e.ids=[689],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},72414:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>g,requestAsyncStorage:()=>j,routeModule:()=>f,serverHooks:()=>_,staticGenerationAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>m,PUT:()=>x});var n=t(49303),i=t(88716),a=t(60670),o=t(87070),p=t(45609),u=t(44644),l=t(13538),d=t(9133);let c=d.z.object({status:d.z.enum(["pending","accepted","rejected"]).optional(),cover_letter:d.z.string().optional(),resume:d.z.string().optional()});async function m(e,{params:r}){try{let e=await (0,p.getServerSession)(u.authOptions);if(!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=parseInt(r.id);if(isNaN(t))return o.NextResponse.json({error:"Invalid application ID"},{status:400});let s=await l._.jobApplication.findUnique({where:{id:t},include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}},applicant:{select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0,employee_profile:{select:{skills:!0,experience:!0,education:!0}}}}}});if(!s)return o.NextResponse.json({error:"Application not found"},{status:404});let n="employee"===e.user.userType&&s.applicant_id===parseInt(e.user.id),i="employer"===e.user.userType&&s.job.employer_id===parseInt(e.user.id);if(!n&&!i)return o.NextResponse.json({error:"Unauthorized"},{status:403});let a={...s,job_details:s.job,applicant_details:s.applicant};return o.NextResponse.json({application:a})}catch(e){return console.error("Error fetching application:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,p.getServerSession)(u.authOptions);if(!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=parseInt(r.id);if(isNaN(s))return o.NextResponse.json({error:"Invalid application ID"},{status:400});let n=await l._.jobApplication.findUnique({where:{id:s},include:{job:!0}});if(!n)return o.NextResponse.json({error:"Application not found"},{status:404});let i=await e.json(),a=c.parse(i);if(a.status){if("employer"!==t.user.userType||n.job.employer_id!==parseInt(t.user.id))return o.NextResponse.json({error:"Unauthorized"},{status:403})}else if("employee"!==t.user.userType||n.applicant_id!==parseInt(t.user.id))return o.NextResponse.json({error:"Unauthorized"},{status:403});let d=await l._.jobApplication.update({where:{id:s},data:a,include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}},applicant:{select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0}}}});return a.status&&await l._.notification.create({data:{user_id:n.applicant_id,notification_type:"application",message:`Your application for "${n.job.title}" has been ${a.status}`}}),o.NextResponse.json({message:"Application updated successfully",application:{...d,job_details:d.job,applicant_details:d.applicant}})}catch(e){if(console.error("Error updating application:",e),e instanceof d.z.ZodError)return o.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:r}){try{let e=await (0,p.getServerSession)(u.authOptions);if(!e||"employee"!==e.user.userType)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=parseInt(r.id);if(isNaN(t))return o.NextResponse.json({error:"Invalid application ID"},{status:400});let s=await l._.jobApplication.findUnique({where:{id:t}});if(!s)return o.NextResponse.json({error:"Application not found"},{status:404});if(s.applicant_id!==parseInt(e.user.id))return o.NextResponse.json({error:"Unauthorized"},{status:403});return await l._.jobApplication.delete({where:{id:t}}),o.NextResponse.json({message:"Application withdrawn successfully"})}catch(e){return console.error("Error deleting application:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/applications/[id]/route",pathname:"/api/applications/[id]",filename:"route",bundlePath:"app/api/applications/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\applications\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:j,staticGenerationAsyncStorage:h,serverHooks:_}=f,q="/api/applications/[id]/route";function g(){return(0,a.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:h})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),n=t.n(s),i=t(53797),a=t(13538),o=t(98691);let p={providers:[(0,i.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await a._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await o.ZP.compare(e.password,r.password))return null;return await a._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=n()(p)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,133,538],()=>t(72414));module.exports=s})();