"use strict";(()=>{var e={};e.id=617,e.ids=[617],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},26701:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>g,requestAsyncStorage:()=>x,routeModule:()=>d,serverHooks:()=>y,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>c});var i=t(49303),a=t(88716),o=t(60670),n=t(87070),p=t(45609),u=t(44644),l=t(13538);async function c(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r||"employer"!==r.user.userType)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"10"),a=t.get("status"),o=t.get("job_id"),c=(s-1)*i,d={job:{employer_id:parseInt(r.user.id)}};a&&(d.status=a),o&&(d.job_id=parseInt(o));let[x,m]=await Promise.all([l._.jobApplication.findMany({where:d,include:{job:{select:{id:!0,title:!0,company:!0,location:!0,status:!0}},applicant:{select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0,employee_profile:{select:{skills:!0,experience:!0,education:!0}}}}},orderBy:{applied_at:"desc"},skip:c,take:i}),l._.jobApplication.count({where:d})]),y=x.map(e=>({...e,job_details:e.job,applicant_details:e.applicant}));return n.NextResponse.json({applications:y,pagination:{page:s,limit:i,total:m,pages:Math.ceil(m/i)}})}catch(e){return console.error("Error fetching received applications:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/applications/received/route",pathname:"/api/applications/received",filename:"route",bundlePath:"app/api/applications/received/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\applications\\received\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:m,serverHooks:y}=d,q="/api/applications/received/route";function g(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:m})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),i=t.n(s),a=t(53797),o=t(13538),n=t(98691);let p={providers:[(0,a.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await o._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await n.ZP.compare(e.password,r.password))return null;return await o._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=i()(p)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,538],()=>t(26701));module.exports=s})();