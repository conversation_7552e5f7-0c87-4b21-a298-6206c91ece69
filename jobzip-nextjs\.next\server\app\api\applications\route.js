"use strict";(()=>{var e={};e.id=569,e.ids=[569],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},60800:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>h,requestAsyncStorage:()=>y,routeModule:()=>_,serverHooks:()=>g,staticGenerationAsyncStorage:()=>j});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>x});var a=t(49303),i=t(88716),o=t(60670),n=t(87070),p=t(45609),u=t(44644),l=t(13538),c=t(9133);let d=c.z.object({job_id:c.z.number(),cover_letter:c.z.string().optional(),resume:c.z.string().optional()});async function m(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),i=t.get("status"),o=(s-1)*a,c={};"employee"===r.user.userType?c.applicant_id=parseInt(r.user.id):c.job={employer_id:parseInt(r.user.id)},i&&(c.status=i);let[d,m]=await Promise.all([l._.jobApplication.findMany({where:c,include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}},applicant:{select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0,employee_profile:!0}}},orderBy:{applied_at:"desc"},skip:o,take:a}),l._.jobApplication.count({where:c})]),x=d.map(e=>({...e,job_details:e.job,applicant_details:e.applicant}));return n.NextResponse.json({applications:x,pagination:{page:s,limit:a,total:m,pages:Math.ceil(m/a)}})}catch(e){return console.error("Error fetching applications:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r||"employee"!==r.user.userType)return n.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),s=d.parse(t),a=await l._.job.findUnique({where:{id:s.job_id}});if(!a)return n.NextResponse.json({error:"Job not found"},{status:404});if("open"!==a.status)return n.NextResponse.json({error:"Job is no longer accepting applications"},{status:400});if(await l._.jobApplication.findUnique({where:{job_id_applicant_id:{job_id:s.job_id,applicant_id:parseInt(r.user.id)}}}))return n.NextResponse.json({error:"You have already applied to this job"},{status:400});let i=await l._.jobApplication.create({data:{...s,applicant_id:parseInt(r.user.id)},include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}},applicant:{select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0}}}});return await l._.notification.create({data:{user_id:a.employer_id,notification_type:"listing",message:`${r.user.username} has applied to your job: ${a.title}`}}),n.NextResponse.json({message:"Application submitted successfully",application:{...i,job_details:i.job,applicant_details:i.applicant}})}catch(e){if(console.error("Error creating application:",e),e instanceof c.z.ZodError)return n.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/applications/route",pathname:"/api/applications",filename:"route",bundlePath:"app/api/applications/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\applications\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:j,serverHooks:g}=_,f="/api/applications/route";function h(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:j})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),a=t.n(s),i=t(53797),o=t(13538),n=t(98691);let p={providers:[(0,i.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await o._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await n.ZP.compare(e.password,r.password))return null;return await o._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=a()(p)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,133,538],()=>t(60800));module.exports=s})();