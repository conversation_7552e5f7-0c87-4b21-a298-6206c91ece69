"use strict";(()=>{var e={};e.id=912,e.ids=[912],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},23239:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>l,patchFetch:()=>x,requestAsyncStorage:()=>u,routeModule:()=>i,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p});var s=t(49303),a=t(88716),o=t(60670),n=t(44644);let i=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:d}=i,l="/api/auth/[...nextauth]/route";function x(){return(0,o.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:p})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>p,POST:()=>p,authOptions:()=>u});var s=t(75571),a=t.n(s),o=t(53797),n=t(13538),i=t(98691);let u={providers:[(0,o.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await n._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await i.ZP.compare(e.password,r.password))return null;return await n._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},p=a()(u)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,790,538],()=>t(23239));module.exports=s})();