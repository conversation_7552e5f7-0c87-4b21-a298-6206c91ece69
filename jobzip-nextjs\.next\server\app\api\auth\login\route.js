"use strict";(()=>{var e={};e.id=873,e.ids=[873],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},16187:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>f,requestAsyncStorage:()=>h,routeModule:()=>x,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var o={};t.r(o),t.d(o,{POST:()=>c});var s=t(49303),n=t(88716),i=t(60670),a=t(87070),p=t(13538),u=t(98691),d=t(9133);let l=d.z.object({username:d.z.string().min(1),password:d.z.string().min(1)});async function c(e){try{let r=await e.json(),t=l.parse(r),o=await p._.user.findUnique({where:{username:t.username},include:{employer_profile:!0,employee_profile:!0}});if(!o||!o.is_active||!await u.ZP.compare(t.password,o.password))return a.NextResponse.json({error:"Invalid credentials"},{status:401});await p._.user.update({where:{id:o.id},data:{last_login:new Date}});let{password:s,...n}=o;return a.NextResponse.json({message:"Login successful",user:n})}catch(e){if(console.error("Login error:",e),e instanceof d.z.ZodError)return a.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:g}=x,q="/api/auth/login/route";function f(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[276,901,70,133,538],()=>t(16187));module.exports=o})();