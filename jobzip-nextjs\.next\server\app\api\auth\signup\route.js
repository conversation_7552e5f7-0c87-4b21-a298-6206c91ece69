"use strict";(()=>{var e={};e.id=654,e.ids=[654],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},55704:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>g,requestAsyncStorage:()=>x,routeModule:()=>c,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>l});var a=t(49303),o=t(88716),n=t(60670),i=t(87070),p=t(13538),u=t(98691),d=t(9133);let m=d.z.object({username:d.z.string().min(3).max(150),email:d.z.string().email(),password:d.z.string().min(6),user_type:d.z.enum(["employee","employer"]),first_name:d.z.string().optional(),last_name:d.z.string().optional(),company_name:d.z.string().optional()});async function l(e){try{let r=await e.json(),t=m.parse(r);if(await p._.user.findFirst({where:{OR:[{username:t.username},{email:t.email}]}}))return i.NextResponse.json({error:"User with this username or email already exists"},{status:400});let s=await u.ZP.hash(t.password,12),a=await p._.user.create({data:{username:t.username,email:t.email,password:s,user_type:t.user_type,first_name:t.first_name,last_name:t.last_name}});"employer"===t.user_type?await p._.employerProfile.create({data:{user_id:a.id,company_name:t.company_name||""}}):await p._.employeeProfile.create({data:{user_id:a.id}});let{password:o,...n}=a;return i.NextResponse.json({message:"User created successfully",user:n})}catch(e){if(console.error("Signup error:",e),e instanceof d.z.ZodError)return i.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return i.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:h,serverHooks:y}=c,_="/api/auth/signup/route";function g(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,133,538],()=>t(55704));module.exports=s})();