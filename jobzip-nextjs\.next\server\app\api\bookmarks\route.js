"use strict";(()=>{var e={};e.id=324,e.ids=[324],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},9178:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>y,routeModule:()=>k,serverHooks:()=>j,staticGenerationAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{DELETE:()=>b,GET:()=>m,POST:()=>x});var o=t(49303),a=t(88716),n=t(60670),i=t(87070),u=t(45609),p=t(44644),d=t(13538),l=t(9133);let c=l.z.object({job_id:l.z.number()});async function m(e){try{let r=await (0,u.getServerSession)(p.authOptions);if(!r||"employee"!==r.user.userType)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"10"),a=(s-1)*o,[n,l]=await Promise.all([d._.bookmark.findMany({where:{user_id:parseInt(r.user.id)},include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}},_count:{select:{applications:!0}}}}},orderBy:{created_at:"desc"},skip:a,take:o}),d._.bookmark.count({where:{user_id:parseInt(r.user.id)}})]),c=n.map(e=>({...e.job,employer_details:e.job.employer,applications_count:e.job._count.applications,is_bookmarked:!0}));return i.NextResponse.json({jobs:c,pagination:{page:s,limit:o,total:l,pages:Math.ceil(l/o)}})}catch(e){return console.error("Error fetching bookmarks:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let r=await (0,u.getServerSession)(p.authOptions);if(!r||"employee"!==r.user.userType)return i.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),s=c.parse(t),o=await d._.job.findUnique({where:{id:s.job_id}});if(!o)return i.NextResponse.json({error:"Job not found"},{status:404});if(await d._.bookmark.findFirst({where:{user_id:parseInt(r.user.id),job_id:s.job_id}}))return i.NextResponse.json({error:"Job already bookmarked"},{status:400});let a=await d._.bookmark.create({data:{user_id:parseInt(r.user.id),job_id:s.job_id},include:{job:{include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}}}});return await d._.notification.create({data:{user_id:parseInt(r.user.id),notification_type:"bookmark",message:`You bookmarked the job: ${o.title}`}}),i.NextResponse.json({message:"Job bookmarked successfully",bookmark:{...a,job_details:a.job}})}catch(e){if(console.error("Error creating bookmark:",e),e instanceof l.z.ZodError)return i.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function b(e){try{let r=await (0,u.getServerSession)(p.authOptions);if(!r||"employee"!==r.user.userType)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("job_id")||"0");if(!s)return i.NextResponse.json({error:"Job ID is required"},{status:400});let o=await d._.bookmark.findFirst({where:{user_id:parseInt(r.user.id),job_id:s}});if(!o)return i.NextResponse.json({error:"Bookmark not found"},{status:404});return await d._.bookmark.delete({where:{id:o.id}}),i.NextResponse.json({message:"Bookmark removed successfully"})}catch(e){return console.error("Error removing bookmark:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let k=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/bookmarks/route",pathname:"/api/bookmarks",filename:"route",bundlePath:"app/api/bookmarks/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\bookmarks\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:_,serverHooks:j}=k,h="/api/bookmarks/route";function f(){return(0,n.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:_})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>p,POST:()=>p,authOptions:()=>u});var s=t(75571),o=t.n(s),a=t(53797),n=t(13538),i=t(98691);let u={providers:[(0,a.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await n._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await i.ZP.compare(e.password,r.password))return null;return await n._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},p=o()(u)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,133,538],()=>t(9178));module.exports=s})();