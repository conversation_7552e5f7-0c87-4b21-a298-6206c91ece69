"use strict";(()=>{var e={};e.id=301,e.ids=[301],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},35372:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>b,requestAsyncStorage:()=>m,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>l});var o=t(49303),a=t(88716),n=t(60670),i=t(87070),u=t(45609),p=t(44644),d=t(13538);async function l(e,{params:r}){try{let e=await (0,u.getServerSession)(p.authOptions);if(!e||"employee"!==e.user.userType)return i.NextResponse.json({error:"Unauthorized"},{status:401});let t=parseInt(r.jobId);if(isNaN(t))return i.NextResponse.json({error:"Invalid job ID"},{status:400});if(!await d._.job.findUnique({where:{id:t}}))return i.NextResponse.json({error:"Job not found"},{status:404});let s=parseInt(e.user.id),o=await d._.bookmark.findFirst({where:{user_id:s,job_id:t}});if(o)return await d._.bookmark.delete({where:{id:o.id}}),i.NextResponse.json({message:"Bookmark removed",bookmarked:!1});return await d._.bookmark.create({data:{user_id:s,job_id:t}}),i.NextResponse.json({message:"Job bookmarked",bookmarked:!0})}catch(e){return console.error("Error toggling bookmark:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/bookmarks/toggle/[jobId]/route",pathname:"/api/bookmarks/toggle/[jobId]",filename:"route",bundlePath:"app/api/bookmarks/toggle/[jobId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\bookmarks\\toggle\\[jobId]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:g}=c,q="/api/bookmarks/toggle/[jobId]/route";function b(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>p,POST:()=>p,authOptions:()=>u});var s=t(75571),o=t.n(s),a=t(53797),n=t(13538),i=t(98691);let u={providers:[(0,a.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await n._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await i.ZP.compare(e.password,r.password))return null;return await n._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},p=o()(u)}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,538],()=>t(35372));module.exports=s})();