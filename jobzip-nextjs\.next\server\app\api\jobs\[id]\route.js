"use strict";(()=>{var e={};e.id=788,e.ids=[788],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},1996:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>b,patchFetch:()=>q,requestAsyncStorage:()=>_,routeModule:()=>j,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>m,PUT:()=>x});var o=t(49303),n=t(88716),i=t(60670),a=t(87070),p=t(45609),u=t(44644),l=t(13538),d=t(9133);let c=d.z.object({title:d.z.string().min(1).max(200).optional(),description:d.z.string().min(1).optional(),location:d.z.string().min(1).max(100).optional(),duration:d.z.string().min(1).max(50).optional(),company:d.z.string().min(1).max(200).optional(),salary:d.z.string().optional(),employees_required:d.z.number().min(1).optional(),deadline:d.z.string().datetime().optional(),status:d.z.enum(["open","closed"]).optional()});async function m(e,{params:r}){try{let e=parseInt(r.id);if(isNaN(e))return a.NextResponse.json({error:"Invalid job ID"},{status:400});let t=await l._.job.findUnique({where:{id:e},include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0,company_description:!0,website:!0,location:!0}}}},_count:{select:{applications:!0}}}});if(!t)return a.NextResponse.json({error:"Job not found"},{status:404});let s=await (0,p.getServerSession)(u.authOptions),o=!1,n=!1;s&&"employee"===s.user.userType&&(o=!!await l._.jobApplication.findUnique({where:{job_id_applicant_id:{job_id:e,applicant_id:parseInt(s.user.id)}}}),n=!!await l._.bookmark.findFirst({where:{user_id:parseInt(s.user.id),job_id:e}}));let i={...t,employer_details:t.employer,applications_count:t._count.applications,has_applied:o,is_bookmarked:n};return a.NextResponse.json({job:i})}catch(e){return console.error("Error fetching job:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,p.getServerSession)(u.authOptions);if(!t||"employer"!==t.user.userType)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=parseInt(r.id);if(isNaN(s))return a.NextResponse.json({error:"Invalid job ID"},{status:400});let o=await l._.job.findUnique({where:{id:s}});if(!o)return a.NextResponse.json({error:"Job not found"},{status:404});if(o.employer_id!==parseInt(t.user.id))return a.NextResponse.json({error:"Unauthorized"},{status:403});let n=await e.json(),i=c.parse(n),d={...i};i.deadline&&(d.deadline=new Date(i.deadline));let m=await l._.job.update({where:{id:s},data:d,include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}},_count:{select:{applications:!0}}}});return a.NextResponse.json({message:"Job updated successfully",job:{...m,employer_details:m.employer,applications_count:m._count.applications}})}catch(e){if(console.error("Error updating job:",e),e instanceof d.z.ZodError)return a.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:r}){try{let e=await (0,p.getServerSession)(u.authOptions);if(!e||"employer"!==e.user.userType)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=parseInt(r.id);if(isNaN(t))return a.NextResponse.json({error:"Invalid job ID"},{status:400});let s=await l._.job.findUnique({where:{id:t}});if(!s)return a.NextResponse.json({error:"Job not found"},{status:404});if(s.employer_id!==parseInt(e.user.id))return a.NextResponse.json({error:"Unauthorized"},{status:403});return await l._.job.delete({where:{id:t}}),a.NextResponse.json({message:"Job deleted successfully"})}catch(e){return console.error("Error deleting job:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let j=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/jobs/[id]/route",pathname:"/api/jobs/[id]",filename:"route",bundlePath:"app/api/jobs/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\jobs\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:_,staticGenerationAsyncStorage:f,serverHooks:h}=j,b="/api/jobs/[id]/route";function q(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),o=t.n(s),n=t(53797),i=t(13538),a=t(98691);let p={providers:[(0,n.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await i._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await a.ZP.compare(e.password,r.password))return null;return await i._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=o()(p)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,133,538],()=>t(1996));module.exports=s})();