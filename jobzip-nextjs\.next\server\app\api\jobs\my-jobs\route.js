"use strict";(()=>{var e={};e.id=615,e.ids=[615],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},65783:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>y,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var o=t(49303),n=t(88716),a=t(60670),i=t(87070),p=t(45609),u=t(44644),l=t(13538);async function c(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r||"employer"!==r.user.userType)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"10"),n=t.get("status"),a=(s-1)*o,c={employer_id:parseInt(r.user.id)};n&&(c.status=n);let[d,m]=await Promise.all([l._.job.findMany({where:c,include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}},_count:{select:{applications:!0}}},orderBy:{created_at:"desc"},skip:a,take:o}),l._.job.count({where:c})]),x=d.map(e=>({...e,employer_details:e.employer,applications_count:e._count.applications}));return i.NextResponse.json({jobs:x,pagination:{page:s,limit:o,total:m,pages:Math.ceil(m/o)}})}catch(e){return console.error("Error fetching employer jobs:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/jobs/my-jobs/route",pathname:"/api/jobs/my-jobs",filename:"route",bundlePath:"app/api/jobs/my-jobs/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\jobs\\my-jobs\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:y}=d,q="/api/jobs/my-jobs/route";function g(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:x})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),o=t.n(s),n=t(53797),a=t(13538),i=t(98691);let p={providers:[(0,n.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await a._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await i.ZP.compare(e.password,r.password))return null;return await a._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=o()(p)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,538],()=>t(65783));module.exports=s})();