"use strict";(()=>{var e={};e.id=204,e.ids=[204],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},75500:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>j,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>y,serverHooks:()=>h,staticGenerationAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>x});var n=t(49303),o=t(88716),i=t(60670),a=t(87070),p=t(45609),u=t(44644),l=t(13538),c=t(9133);let d=c.z.object({title:c.z.string().min(1).max(200),description:c.z.string().min(1),location:c.z.string().min(1).max(100),duration:c.z.string().min(1).max(50),company:c.z.string().min(1).max(200),salary:c.z.string().optional(),employees_required:c.z.number().min(1),deadline:c.z.string().datetime()});async function m(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),n=r.get("status"),o=r.get("search"),i=r.get("location"),p=(t-1)*s,u={};n&&(u.status=n),o&&(u.OR=[{title:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}},{company:{contains:o,mode:"insensitive"}}]),i&&(u.location={contains:i,mode:"insensitive"});let[c,d]=await Promise.all([l._.job.findMany({where:u,include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}},_count:{select:{applications:!0}}},orderBy:{created_at:"desc"},skip:p,take:s}),l._.job.count({where:u})]),m=c.map(e=>({...e,employer_details:e.employer,applications_count:e._count.applications}));return a.NextResponse.json({jobs:m,pagination:{page:t,limit:s,total:d,pages:Math.ceil(d/s)}})}catch(e){return console.error("Error fetching jobs:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r||"employer"!==r.user.userType)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),s=d.parse(t),n=await l._.job.create({data:{...s,employer_id:parseInt(r.user.id),deadline:new Date(s.deadline)},include:{employer:{select:{id:!0,username:!0,employer_profile:{select:{company_name:!0}}}}}});return a.NextResponse.json({message:"Job created successfully",job:n})}catch(e){if(console.error("Error creating job:",e),e instanceof c.z.ZodError)return a.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/jobs/route",pathname:"/api/jobs",filename:"route",bundlePath:"app/api/jobs/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\jobs\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:q,serverHooks:h}=y,j="/api/jobs/route";function _(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:q})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var s=t(75571),n=t.n(s),o=t(53797),i=t(13538),a=t(98691);let p={providers:[(0,o.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await i._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await a.ZP.compare(e.password,r.password))return null;return await i._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=n()(p)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,133,538],()=>t(75500));module.exports=s})();