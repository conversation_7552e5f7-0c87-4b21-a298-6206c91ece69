"use strict";(()=>{var e={};e.id=69,e.ids=[69],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},65071:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>x,serverHooks:()=>y,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>l});var n=t(49303),i=t(88716),a=t(60670),o=t(87070),u=t(45609),p=t(44644),d=t(13538);async function c(e){try{let r=await (0,u.getServerSession)(p.authOptions);if(!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"20"),i="true"===t.get("unread_only"),a=(s-1)*n,c={user_id:parseInt(r.user.id)};i&&(c.is_read=!1);let[l,x,m]=await Promise.all([d._.notification.findMany({where:c,orderBy:{created_at:"desc"},skip:a,take:n}),d._.notification.count({where:c}),d._.notification.count({where:{user_id:parseInt(r.user.id),is_read:!1}})]);return o.NextResponse.json({notifications:l,pagination:{page:s,limit:n,total:x,pages:Math.ceil(x/n)},unread_count:m})}catch(e){return console.error("Error fetching notifications:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=await (0,u.getServerSession)(p.authOptions);if(!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{notification_ids:t,mark_all_read:s}=await e.json();if(s)return await d._.notification.updateMany({where:{user_id:parseInt(r.user.id),is_read:!1},data:{is_read:!0}}),o.NextResponse.json({message:"All notifications marked as read"});if(t&&Array.isArray(t))return await d._.notification.updateMany({where:{id:{in:t},user_id:parseInt(r.user.id)},data:{is_read:!0}}),o.NextResponse.json({message:"Notifications marked as read"});return o.NextResponse.json({error:"Invalid request data"},{status:400})}catch(e){return console.error("Error updating notifications:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/user/notifications/route",pathname:"/api/user/notifications",filename:"route",bundlePath:"app/api/user/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\user\\notifications\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:y}=x,h="/api/user/notifications/route";function g(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:f})}},44644:(e,r,t)=>{t.r(r),t.d(r,{GET:()=>p,POST:()=>p,authOptions:()=>u});var s=t(75571),n=t.n(s),i=t(53797),a=t(13538),o=t(98691);let u={providers:[(0,i.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await a._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await o.ZP.compare(e.password,r.password))return null;return await a._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},p=n()(u)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,901,70,790,538],()=>t(65071));module.exports=s})();