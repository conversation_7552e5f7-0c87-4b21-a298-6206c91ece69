"use strict";(()=>{var e={};e.id=744,e.ids=[744],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},92761:e=>{e.exports=require("node:async_hooks")},17718:e=>{e.exports=require("node:child_process")},6005:e=>{e.exports=require("node:crypto")},15673:e=>{e.exports=require("node:events")},87561:e=>{e.exports=require("node:fs")},93977:e=>{e.exports=require("node:fs/promises")},70612:e=>{e.exports=require("node:os")},49411:e=>{e.exports=require("node:path")},97742:e=>{e.exports=require("node:process")},25997:e=>{e.exports=require("node:tty")},47261:e=>{e.exports=require("node:util")},94277:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>v,patchFetch:()=>j,requestAsyncStorage:()=>g,routeModule:()=>w,serverHooks:()=>q,staticGenerationAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{GET:()=>x,POST:()=>f,PUT:()=>y});var o=s(49303),i=s(88716),n=s(60670),a=s(87070),p=s(45609),u=s(44644),l=s(13538),d=s(9133),c=s(98691);let m=d.z.object({first_name:d.z.string().optional(),last_name:d.z.string().optional(),email:d.z.string().email().optional(),location:d.z.string().optional(),bio:d.z.string().optional(),skills:d.z.string().optional(),experience:d.z.string().optional(),education:d.z.string().optional(),company_name:d.z.string().optional(),company_description:d.z.string().optional(),website:d.z.string().optional()}),_=d.z.object({current_password:d.z.string().min(1),new_password:d.z.string().min(6)});async function x(e){try{let e=await (0,p.getServerSession)(u.authOptions);if(!e)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await l._.user.findUnique({where:{id:parseInt(e.user.id)},select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0,user_type:!0,location:!0,bio:!0,profile_picture:!0,date_joined:!0,last_login:!0,employee_profile:{select:{skills:!0,experience:!0,education:!0}},employer_profile:{select:{company_name:!0,company_description:!0,website:!0,location:!0}}}});if(!r)return a.NextResponse.json({error:"User not found"},{status:404});return a.NextResponse.json({user:r})}catch(e){return console.error("Error fetching user profile:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),t=m.parse(s),o=parseInt(r.user.id),i={},n={};void 0!==t.first_name&&(i.first_name=t.first_name),void 0!==t.last_name&&(i.last_name=t.last_name),void 0!==t.email&&(i.email=t.email),void 0!==t.location&&(i.location=t.location),void 0!==t.bio&&(i.bio=t.bio),"employee"===r.user.userType?(void 0!==t.skills&&(n.skills=t.skills),void 0!==t.experience&&(n.experience=t.experience),void 0!==t.education&&(n.education=t.education)):"employer"===r.user.userType&&(void 0!==t.company_name&&(n.company_name=t.company_name),void 0!==t.company_description&&(n.company_description=t.company_description),void 0!==t.website&&(n.website=t.website),void 0!==t.location&&(n.location=t.location)),Object.keys(i).length>0&&await l._.user.update({where:{id:o},data:i}),Object.keys(n).length>0&&("employee"===r.user.userType?await l._.employeeProfile.upsert({where:{user_id:o},update:n,create:{user_id:o,...n}}):"employer"===r.user.userType&&await l._.employerProfile.upsert({where:{user_id:o},update:n,create:{user_id:o,...n}}));let d=await l._.user.findUnique({where:{id:o},select:{id:!0,username:!0,email:!0,first_name:!0,last_name:!0,user_type:!0,location:!0,bio:!0,profile_picture:!0,date_joined:!0,last_login:!0,employee_profile:{select:{skills:!0,experience:!0,education:!0}},employer_profile:{select:{company_name:!0,company_description:!0,website:!0,location:!0}}}});return a.NextResponse.json({message:"Profile updated successfully",user:d})}catch(e){if(console.error("Error updating user profile:",e),e instanceof d.z.ZodError)return a.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let r=await (0,p.getServerSession)(u.authOptions);if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),t=_.parse(s),o=parseInt(r.user.id),i=await l._.user.findUnique({where:{id:o},select:{password:!0}});if(!i)return a.NextResponse.json({error:"User not found"},{status:404});if(!await c.ZP.compare(t.current_password,i.password))return a.NextResponse.json({error:"Current password is incorrect"},{status:400});let n=await c.ZP.hash(t.new_password,12);return await l._.user.update({where:{id:o},data:{password:n}}),a.NextResponse.json({message:"Password changed successfully"})}catch(e){if(console.error("Error changing password:",e),e instanceof d.z.ZodError)return a.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:q}=w,v="/api/user/profile/route";function j(){return(0,n.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:h})}},44644:(e,r,s)=>{s.r(r),s.d(r,{GET:()=>u,POST:()=>u,authOptions:()=>p});var t=s(75571),o=s.n(t),i=s(53797),n=s(13538),a=s(98691);let p={providers:[(0,i.Z)({name:"Credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.username||!e?.password)return null;try{let r=await n._.user.findUnique({where:{username:e.username},include:{employer_profile:!0,employee_profile:!0}});if(!r||!r.is_active||!await a.ZP.compare(e.password,r.password))return null;return await n._.user.update({where:{id:r.id},data:{last_login:new Date}}),{id:r.id.toString(),username:r.username,email:r.email,userType:r.user_type,name:r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username}}catch(e){return console.error("Authentication error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id,e.userType=r.userType),e),session:async({session:e,token:r})=>(e.user&&(e.user.id=r.id,e.user.userType=r.userType),e)},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET},u=o()(p)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,901,70,790,133,538],()=>s(94277));module.exports=t})();