(()=>{var e={};e.id=716,e.ids=[716],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71449:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(95293),s(64968),s(35866);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,95293)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\auth\\login\\page.tsx"],u="/auth/login/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2228:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},76165:(e,r,s)=>{Promise.resolve().then(s.bind(s,11012))},60415:(e,r,s)=>{Promise.resolve().then(s.bind(s,47073))},47073:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(10326),a=s(17577),n=s(77109),i=s(35047),o=s(90434),l=s(74723),d=s(74064),c=s(42576);let u=c.z.object({username:c.z.string().min(1,"Username is required"),password:c.z.string().min(1,"Password is required")});function m(){let[e,r]=(0,a.useState)(!1),[s,c]=(0,a.useState)(""),m=(0,i.useRouter)(),{register:p,handleSubmit:x,formState:{errors:h}}=(0,l.cI)({resolver:(0,d.F)(u)}),g=async e=>{r(!0),c("");try{let r=await (0,n.signIn)("credentials",{username:e.username,password:e.password,redirect:!1});if(r?.error)c("Invalid username or password");else{let e=await (0,n.getSession)();e?.user?.userType==="employer"?m.push("/employer/dashboard"):m.push("/employee/dashboard")}}catch(e){c("An error occurred. Please try again.")}finally{r(!1)}};return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-dark-darker",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[t.jsx("i",{className:"fas fa-briefcase text-3xl text-primary mr-3"}),t.jsx("h1",{className:"text-3xl font-bold",children:"JobZip"})]}),t.jsx("h2",{className:"text-xl text-gray-300",children:"Sign in to your account"})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x(g),children:[s&&t.jsx("div",{className:"bg-red-500/10 border border-red-500 text-red-500 px-4 py-3 rounded",children:s}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300",children:"Username"}),t.jsx("input",{...p("username"),type:"text",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter your username"}),h.username&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.username.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"Password"}),t.jsx("input",{...p("password"),type:"password",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter your password"}),h.password&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.password.message})]})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:e,className:"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Signing in...":"Sign In"})}),t.jsx("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",t.jsx(o.default,{href:"/auth/signup",className:"text-primary hover:text-primary-light",children:"Sign up"})]})})]})]})})}},11012:(e,r,s)=>{"use strict";s.d(r,{Providers:()=>o});var t=s(10326),a=s(77109),n=s(2994),i=s(17577);function o({children:e}){let[r]=(0,i.useState)(()=>new n.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return t.jsx(a.SessionProvider,{children:t.jsx(n.QueryClientProvider,{client:r,children:e})})}},95293:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\auth\login\page.tsx#default`)},64968:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l,metadata:()=>o});var t=s(19510),a=s(25384),n=s.n(a);s(5023);let i=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),o={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function l({children:e}){return(0,t.jsxs)("html",{lang:"en",className:"dark",children:[t.jsx("head",{children:t.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),t.jsx("body",{className:`${n().className} bg-dark-darker text-white min-h-screen`,children:t.jsx(i,{children:e})})]})}},5023:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,539,404,761],()=>s(71449));module.exports=t})();