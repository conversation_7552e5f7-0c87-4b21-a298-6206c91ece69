(()=>{var e={};e.id=271,e.ids=[271],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},95313:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(93722),s(64968),s(35866);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93722)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\auth\\signup\\page.tsx"],m="/auth/signup/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2228:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},76165:(e,r,s)=>{Promise.resolve().then(s.bind(s,11012))},26936:(e,r,s)=>{Promise.resolve().then(s.bind(s,12058))},12058:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(10326),a=s(17577),n=s(35047),i=s(90434),o=s(74723),l=s(74064),d=s(42576);let c=d.z.object({username:d.z.string().min(3,"Username must be at least 3 characters"),email:d.z.string().email("Invalid email address"),password:d.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:d.z.string(),user_type:d.z.enum(["employee","employer"],{required_error:"Please select account type"}),first_name:d.z.string().optional(),last_name:d.z.string().optional(),company_name:d.z.string().optional()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>"employer"!==e.user_type||!!e.company_name,{message:"Company name is required for employers",path:["company_name"]});function m(){let[e,r]=(0,a.useState)(!1),[s,d]=(0,a.useState)(""),m=(0,n.useRouter)(),{register:p,handleSubmit:u,watch:x,formState:{errors:h}}=(0,o.cI)({resolver:(0,l.F)(c)}),y=x("user_type"),g=async e=>{r(!0),d("");try{let r=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e.username,email:e.email,password:e.password,user_type:e.user_type,first_name:e.first_name,last_name:e.last_name,company_name:e.company_name})}),s=await r.json();r.ok?m.push("/auth/login?message=Account created successfully"):d(s.error||"An error occurred during signup")}catch(e){d("An error occurred. Please try again.")}finally{r(!1)}};return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-dark-darker py-12",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[t.jsx("i",{className:"fas fa-briefcase text-3xl text-primary mr-3"}),t.jsx("h1",{className:"text-3xl font-bold",children:"JobZip"})]}),t.jsx("h2",{className:"text-xl text-gray-300",children:"Create your account"})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:u(g),children:[s&&t.jsx("div",{className:"bg-red-500/10 border border-red-500 text-red-500 px-4 py-3 rounded",children:s}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Account Type"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("label",{className:"flex items-center p-3 border border-gray-600 rounded-md cursor-pointer hover:border-primary",children:[t.jsx("input",{...p("user_type"),type:"radio",value:"employee",className:"mr-3 text-primary"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"Employee"}),t.jsx("div",{className:"text-sm text-gray-400",children:"Looking for jobs"})]})]}),(0,t.jsxs)("label",{className:"flex items-center p-3 border border-gray-600 rounded-md cursor-pointer hover:border-primary",children:[t.jsx("input",{...p("user_type"),type:"radio",value:"employer",className:"mr-3 text-primary"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"Employer"}),t.jsx("div",{className:"text-sm text-gray-400",children:"Hiring talent"})]})]})]}),h.user_type&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.user_type.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"first_name",className:"block text-sm font-medium text-gray-300",children:"First Name"}),t.jsx("input",{...p("first_name"),type:"text",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"First name"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"last_name",className:"block text-sm font-medium text-gray-300",children:"Last Name"}),t.jsx("input",{...p("last_name"),type:"text",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Last name"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300",children:"Username *"}),t.jsx("input",{...p("username"),type:"text",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Choose a username"}),h.username&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.username.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:"Email *"}),t.jsx("input",{...p("email"),type:"email",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter your email"}),h.email&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.email.message})]}),"employer"===y&&(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"company_name",className:"block text-sm font-medium text-gray-300",children:"Company Name *"}),t.jsx("input",{...p("company_name"),type:"text",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter company name"}),h.company_name&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.company_name.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"Password *"}),t.jsx("input",{...p("password"),type:"password",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Create a password"}),h.password&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.password.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300",children:"Confirm Password *"}),t.jsx("input",{...p("confirmPassword"),type:"password",className:"mt-1 block w-full px-3 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Confirm your password"}),h.confirmPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.confirmPassword.message})]})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:e,className:"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Creating Account...":"Create Account"})}),t.jsx("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-400",children:["Already have an account?"," ",t.jsx(i.default,{href:"/auth/login",className:"text-primary hover:text-primary-light",children:"Sign in"})]})})]})]})})}},11012:(e,r,s)=>{"use strict";s.d(r,{Providers:()=>o});var t=s(10326),a=s(77109),n=s(2994),i=s(17577);function o({children:e}){let[r]=(0,i.useState)(()=>new n.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return t.jsx(a.SessionProvider,{children:t.jsx(n.QueryClientProvider,{client:r,children:e})})}},93722:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\auth\signup\page.tsx#default`)},64968:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l,metadata:()=>o});var t=s(19510),a=s(25384),n=s.n(a);s(5023);let i=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),o={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function l({children:e}){return(0,t.jsxs)("html",{lang:"en",className:"dark",children:[t.jsx("head",{children:t.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),t.jsx("body",{className:`${n().className} bg-dark-darker text-white min-h-screen`,children:t.jsx(i,{children:e})})]})}},5023:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,539,404,761],()=>s(95313));module.exports=t})();