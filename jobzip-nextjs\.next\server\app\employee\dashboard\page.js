(()=>{var e={};e.id=751,e.ids=[751],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44085:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),a(85408),a(64968),a(35866);var t=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["employee",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,85408)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\employee\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\employee\\dashboard\\page.tsx"],x="/employee/dashboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/employee/dashboard/page",pathname:"/employee/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2228:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},76165:(e,s,a)=>{Promise.resolve().then(a.bind(a,11012))},81774:(e,s,a)=>{Promise.resolve().then(a.bind(a,24932))},90434:(e,s,a)=>{"use strict";a.d(s,{default:()=>r.a});var t=a(79404),r=a.n(t)},35047:(e,s,a)=>{"use strict";var t=a(77389);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},24932:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var t=a(10326),r=a(77109),i=a(35047),l=a(17577),n=a(90434),c=a(11870);function d(){let{data:e,status:s}=(0,r.useSession)();(0,i.useRouter)();let[a,d]=(0,l.useState)([]),[o,x]=(0,l.useState)([]),[m,h]=(0,l.useState)([]),[p,j]=(0,l.useState)(!0);return"loading"===s||p?t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),t.jsx("p",{className:"mt-4 text-gray-400",children:"Loading dashboard..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-dark-darker",children:[t.jsx("header",{className:"bg-dark border-b border-gray-700 px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-white",children:["Welcome back, ",e?.user?.name,"!"]}),t.jsx("p",{className:"text-gray-400",children:"Here's what's happening with your job search"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(c.L,{}),(0,t.jsxs)(n.default,{href:"/profile",className:"btn-outline-primary",children:[t.jsx("i",{className:"fas fa-user mr-2"}),"Profile"]}),(0,t.jsxs)(n.default,{href:"/jobs",className:"btn-primary",children:[t.jsx("i",{className:"fas fa-search mr-2"}),"Browse Jobs"]})]})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[t.jsx("div",{className:"card",children:t.jsx("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-3 bg-primary/20 rounded-full",children:t.jsx("i",{className:"fas fa-file-alt text-primary text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-gray-400 text-sm",children:"Total Applications"}),t.jsx("p",{className:"text-2xl font-bold text-white",children:a.length})]})]})})}),t.jsx("div",{className:"card",children:t.jsx("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-3 bg-warning/20 rounded-full",children:t.jsx("i",{className:"fas fa-bookmark text-warning text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-gray-400 text-sm",children:"Bookmarked Jobs"}),t.jsx("p",{className:"text-2xl font-bold text-white",children:o.length})]})]})})}),t.jsx("div",{className:"card",children:t.jsx("div",{className:"card-body",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-3 bg-success/20 rounded-full",children:t.jsx("i",{className:"fas fa-briefcase text-success text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-gray-400 text-sm",children:"Available Jobs"}),t.jsx("p",{className:"text-2xl font-bold text-white",children:m.length})]})]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-xl font-semibold text-white",children:"Recent Applications"}),t.jsx(n.default,{href:"/employee/applications",className:"text-primary hover:text-primary-light",children:"View All"})]}),0===a.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx("i",{className:"fas fa-file-alt text-4xl text-gray-600 mb-4"}),t.jsx("p",{className:"text-gray-400",children:"No applications yet"}),t.jsx(n.default,{href:"/jobs",className:"btn-primary mt-4",children:"Start Applying"})]}):t.jsx("div",{className:"space-y-4",children:a.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-dark-lighter rounded",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-white",children:e.job_details?.title}),t.jsx("p",{className:"text-sm text-gray-400",children:e.job_details?.company}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Applied ",new Date(e.applied_at).toLocaleDateString()]})]}),t.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${"pending"===e.status?"bg-warning/20 text-warning":"accepted"===e.status?"bg-success/20 text-success":"bg-danger/20 text-danger"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]},e.id))})]})}),t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-xl font-semibold text-white",children:"Bookmarked Jobs"}),t.jsx(n.default,{href:"/employee/bookmarks",className:"text-primary hover:text-primary-light",children:"View All"})]}),0===o.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx("i",{className:"fas fa-bookmark text-4xl text-gray-600 mb-4"}),t.jsx("p",{className:"text-gray-400",children:"No bookmarked jobs"}),t.jsx(n.default,{href:"/jobs",className:"btn-primary mt-4",children:"Browse Jobs"})]}):t.jsx("div",{className:"space-y-4",children:o.slice(0,5).map(e=>t.jsx("div",{className:"p-3 bg-dark-lighter rounded",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("h3",{className:"font-medium text-white",children:e.title}),t.jsx("p",{className:"text-sm text-gray-400",children:e.company}),t.jsx("p",{className:"text-sm text-gray-500",children:e.location}),e.salary&&t.jsx("p",{className:"text-sm text-primary",children:e.salary})]}),t.jsx(n.default,{href:`/jobs/${e.id}`,className:"btn-outline-primary text-xs",children:"View"})]})},e.id))})]})})]}),t.jsx("div",{className:"mt-8",children:t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-xl font-semibold text-white",children:"Recommended Jobs"}),t.jsx(n.default,{href:"/jobs",className:"text-primary hover:text-primary-light",children:"View All"})]}),0===m.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx("i",{className:"fas fa-briefcase text-4xl text-gray-600 mb-4"}),t.jsx("p",{className:"text-gray-400",children:"No jobs available"})]}):t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.slice(0,6).map(e=>(0,t.jsxs)("div",{className:"p-4 bg-dark-lighter rounded",children:[t.jsx("h3",{className:"font-medium text-white mb-2",children:e.title}),t.jsx("p",{className:"text-sm text-gray-400 mb-1",children:e.company}),t.jsx("p",{className:"text-sm text-gray-500 mb-2",children:e.location}),e.salary&&t.jsx("p",{className:"text-sm text-primary mb-3",children:e.salary}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.deadline).toLocaleDateString()}),t.jsx(n.default,{href:`/jobs/${e.id}`,className:"btn-primary text-xs",children:"Apply"})]})]},e.id))})]})})})]})]})}},11870:(e,s,a)=>{"use strict";a.d(s,{L:()=>l});var t=a(10326),r=a(77109),i=a(17577);function l({className:e=""}){let{data:s}=(0,r.useSession)(),[a,l]=(0,i.useState)([]),[n,c]=(0,i.useState)(0),[d,o]=(0,i.useState)(!1),[x,m]=(0,i.useState)(!1),h=async()=>{try{let e=await fetch("/api/user/notifications?limit=10");if(e.ok){let s=await e.json();l(s.notifications||[]),c(s.unread_count||0)}}catch(e){console.error("Error fetching notifications:",e)}},p=async e=>{try{(await fetch("/api/user/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notification_ids:e})})).ok&&h()}catch(e){console.error("Error marking notifications as read:",e)}},j=async()=>{m(!0);try{(await fetch("/api/user/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({mark_all_read:!0})})).ok&&h()}catch(e){console.error("Error marking all notifications as read:",e)}finally{m(!1)}},u=e=>{e.is_read||p([e.id]),o(!1)},f=e=>{switch(e){case"application":return"fas fa-file-alt";case"listing":return"fas fa-briefcase";case"message":return"fas fa-envelope";default:return"fas fa-bell"}},b=e=>{let s=new Date(e),a=Math.floor((new Date().getTime()-s.getTime())/1e3);return a<60?"Just now":a<3600?`${Math.floor(a/60)}m ago`:a<86400?`${Math.floor(a/3600)}h ago`:a<604800?`${Math.floor(a/86400)}d ago`:s.toLocaleDateString()};return s?(0,t.jsxs)("div",{className:`relative ${e}`,children:[(0,t.jsxs)("button",{onClick:()=>o(!d),className:"relative p-2 text-gray-400 hover:text-white transition-colors",children:[t.jsx("i",{className:"fas fa-bell text-xl"}),n>0&&t.jsx("span",{className:"absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:n>9?"9+":n})]}),d&&(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"fixed inset-0 z-10",onClick:()=>o(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-dark border border-gray-700 rounded-lg shadow-lg z-20",children:[t.jsx("div",{className:"p-4 border-b border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("h3",{className:"text-lg font-semibold text-white",children:"Notifications"}),n>0&&t.jsx("button",{onClick:j,disabled:x,className:"text-sm text-primary hover:text-primary-light",children:x?"Marking...":"Mark all read"})]})}),t.jsx("div",{className:"max-h-96 overflow-y-auto",children:0===a.length?(0,t.jsxs)("div",{className:"p-4 text-center text-gray-400",children:[t.jsx("i",{className:"fas fa-bell-slash text-2xl mb-2"}),t.jsx("p",{children:"No notifications"})]}):t.jsx("div",{className:"divide-y divide-gray-700",children:a.map(e=>t.jsx("div",{onClick:()=>u(e),className:`p-4 cursor-pointer hover:bg-dark-lighter transition-colors ${e.is_read?"":"bg-primary/5"}`,children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:`p-2 rounded-full ${e.is_read?"bg-gray-700":"bg-primary/20"}`,children:t.jsx("i",{className:`${f(e.notification_type)} ${e.is_read?"text-gray-400":"text-primary"}`})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[t.jsx("p",{className:`text-sm ${e.is_read?"text-gray-300":"text-white font-medium"}`,children:e.message}),t.jsx("p",{className:"text-xs text-gray-500 mt-1",children:b(e.created_at)})]}),!e.is_read&&t.jsx("div",{className:"w-2 h-2 bg-primary rounded-full"})]})},e.id))})}),a.length>0&&t.jsx("div",{className:"p-4 border-t border-gray-700",children:t.jsx("button",{onClick:()=>{o(!1)},className:"w-full text-center text-sm text-primary hover:text-primary-light",children:"View all notifications"})})]})]})]}):null}},11012:(e,s,a)=>{"use strict";a.d(s,{Providers:()=>n});var t=a(10326),r=a(77109),i=a(2994),l=a(17577);function n({children:e}){let[s]=(0,l.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return t.jsx(r.SessionProvider,{children:t.jsx(i.QueryClientProvider,{client:s,children:e})})}},85408:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\employee\dashboard\page.tsx#default`)},64968:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c,metadata:()=>n});var t=a(19510),r=a(25384),i=a.n(r);a(5023);let l=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),n={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function c({children:e}){return(0,t.jsxs)("html",{lang:"en",className:"dark",children:[t.jsx("head",{children:t.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),t.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:t.jsx(l,{children:e})})]})}},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[276,539,404],()=>a(44085));module.exports=t})();