(()=>{var e={};e.id=997,e.ids=[997],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43717:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(55495),t(64968),t(35866);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["employer",{children:["jobs",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55495)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\employer\\jobs\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\employer\\jobs\\new\\page.tsx"],m="/employer/jobs/new/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/employer/jobs/new/page",pathname:"/employer/jobs/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2228:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},76165:(e,s,t)=>{Promise.resolve().then(t.bind(t,11012))},45684:(e,s,t)=>{Promise.resolve().then(t.bind(t,3524))},90434:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(79404),r=t.n(a)},35047:(e,s,t)=>{"use strict";var a=t(77389);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},3524:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(10326),r=t(77109),i=t(35047),n=t(17577),l=t(90434);function o(){let{data:e,status:s}=(0,r.useSession)(),t=(0,i.useRouter)(),[o,c]=(0,n.useState)(!1),[d,m]=(0,n.useState)({title:"",description:"",location:"",duration:"",company:"",salary:"",employees_required:1,deadline:""}),x=async e=>{e.preventDefault(),c(!0);try{let e=await fetch("/api/jobs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...d,employees_required:parseInt(d.employees_required.toString()),deadline:new Date(d.deadline).toISOString()})});if(e.ok){let s=await e.json();t.push(`/jobs/${s.job.id}`)}else{let s=await e.json();alert(s.error||"Failed to create job posting")}}catch(e){console.error("Error creating job:",e),alert("An error occurred while creating the job posting")}finally{c(!1)}},p=e=>{let{name:s,value:t}=e.target;m(e=>({...e,[s]:t}))};return"loading"===s?a.jsx("div",{className:"min-h-screen bg-dark-darker flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("i",{className:"fas fa-spinner fa-spin text-4xl text-primary mb-4"}),a.jsx("p",{className:"text-gray-400",children:"Loading..."})]})}):e&&"employer"===e.user.userType?(0,a.jsxs)("div",{className:"min-h-screen bg-dark-darker",children:[a.jsx("header",{className:"bg-dark border-b border-gray-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-white",children:"Post a New Job"}),a.jsx("p",{className:"text-gray-400",children:"Create a job posting to find the perfect candidates"})]}),a.jsx("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(l.default,{href:"/employer/dashboard",className:"btn-outline-primary",children:[a.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})})]})}),a.jsx("div",{className:"container mx-auto px-6 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[a.jsx("div",{className:"card",children:a.jsx("div",{className:"card-body",children:(0,a.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Basic Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-300 mb-2",children:"Job Title *"}),a.jsx("input",{type:"text",id:"title",name:"title",value:d.title,onChange:p,required:!0,className:"form-input",placeholder:"e.g. Senior Software Engineer"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-300 mb-2",children:"Company Name *"}),a.jsx("input",{type:"text",id:"company",name:"company",value:d.company,onChange:p,required:!0,className:"form-input",placeholder:"e.g. TechCorp Inc."})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-300 mb-2",children:"Location *"}),a.jsx("input",{type:"text",id:"location",name:"location",value:d.location,onChange:p,required:!0,className:"form-input",placeholder:"e.g. New York, NY or Remote"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"duration",className:"block text-sm font-medium text-gray-300 mb-2",children:"Duration *"}),(0,a.jsxs)("select",{id:"duration",name:"duration",value:d.duration,onChange:p,required:!0,className:"form-input",children:[a.jsx("option",{value:"",children:"Select duration"}),a.jsx("option",{value:"Full-time",children:"Full-time"}),a.jsx("option",{value:"Part-time",children:"Part-time"}),a.jsx("option",{value:"Contract",children:"Contract"}),a.jsx("option",{value:"Temporary",children:"Temporary"}),a.jsx("option",{value:"Internship",children:"Internship"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"salary",className:"block text-sm font-medium text-gray-300 mb-2",children:"Salary (Optional)"}),a.jsx("input",{type:"text",id:"salary",name:"salary",value:d.salary,onChange:p,className:"form-input",placeholder:"e.g. $80,000 - $120,000 per year"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"employees_required",className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Positions *"}),a.jsx("input",{type:"number",id:"employees_required",name:"employees_required",value:d.employees_required,onChange:p,required:!0,min:"1",className:"form-input"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Job Description"}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-300 mb-2",children:"Description *"}),a.jsx("textarea",{id:"description",name:"description",value:d.description,onChange:p,required:!0,rows:10,className:"form-input",placeholder:"Provide a detailed description of the job role, responsibilities, requirements, and qualifications..."}),a.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Include job responsibilities, required skills, qualifications, and any other relevant information."})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Application Details"}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"deadline",className:"block text-sm font-medium text-gray-300 mb-2",children:"Application Deadline *"}),a.jsx("input",{type:"datetime-local",id:"deadline",name:"deadline",value:d.deadline,onChange:p,required:!0,min:new Date().toISOString().slice(0,16),className:"form-input"}),a.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Set the last date and time when applications will be accepted."})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-700",children:[(0,a.jsxs)(l.default,{href:"/employer/dashboard",className:"btn-outline-secondary",children:[a.jsx("i",{className:"fas fa-times mr-2"}),"Cancel"]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{alert("Draft functionality not implemented yet")},className:"btn-outline-primary",children:[a.jsx("i",{className:"fas fa-save mr-2"}),"Save as Draft"]}),a.jsx("button",{type:"submit",disabled:o,className:"btn-primary",children:o?(0,a.jsxs)(a.Fragment,{children:[a.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("i",{className:"fas fa-plus mr-2"}),"Post Job"]})})]})]})]})})}),a.jsx("div",{className:"card mt-8",children:(0,a.jsxs)("div",{className:"card-body",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4",children:[a.jsx("i",{className:"fas fa-lightbulb text-warning mr-2"}),"Tips for a Great Job Posting"]}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx("i",{className:"fas fa-check text-success mr-2 mt-1"}),a.jsx("span",{children:"Write a clear and descriptive job title"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx("i",{className:"fas fa-check text-success mr-2 mt-1"}),a.jsx("span",{children:"Include specific requirements and qualifications"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx("i",{className:"fas fa-check text-success mr-2 mt-1"}),a.jsx("span",{children:"Mention company culture and benefits"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx("i",{className:"fas fa-check text-success mr-2 mt-1"}),a.jsx("span",{children:"Be transparent about salary and compensation"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx("i",{className:"fas fa-check text-success mr-2 mt-1"}),a.jsx("span",{children:"Set a reasonable application deadline"})]})]})]})})]})})]}):null}},11012:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>l});var a=t(10326),r=t(77109),i=t(2994),n=t(17577);function l({children:e}){let[s]=(0,n.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return a.jsx(r.SessionProvider,{children:a.jsx(i.QueryClientProvider,{client:s,children:e})})}},55495:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\employer\jobs\new\page.tsx#default`)},64968:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>l});var a=t(19510),r=t(25384),i=t.n(r);t(5023);let n=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),l={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function o({children:e}){return(0,a.jsxs)("html",{lang:"en",className:"dark",children:[a.jsx("head",{children:a.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),a.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:a.jsx(n,{children:e})})]})}},5023:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,539,404],()=>t(43717));module.exports=a})();