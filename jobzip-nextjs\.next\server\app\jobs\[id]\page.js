(()=>{var e={};e.id=712,e.ids=[712],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25165:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),a(82955),a(64968),a(35866);var t=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(s,o);let c=["",{children:["jobs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,82955)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\jobs\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\jobs\\[id]\\page.tsx"],m="/jobs/[id]/page",x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/jobs/[id]/page",pathname:"/jobs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2228:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},76165:(e,s,a)=>{Promise.resolve().then(a.bind(a,11012))},73626:(e,s,a)=>{Promise.resolve().then(a.bind(a,85028))},90434:(e,s,a)=>{"use strict";a.d(s,{default:()=>r.a});var t=a(79404),r=a.n(t)},35047:(e,s,a)=>{"use strict";var t=a(77389);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})},85028:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(10326),r=a(77109),i=a(35047),l=a(17577),n=a(90434);function o({params:e}){let{data:s,status:a}=(0,r.useSession)(),o=(0,i.useRouter)(),[c,d]=(0,l.useState)(null),[m,x]=(0,l.useState)(!0),[p,h]=(0,l.useState)(!1),[u,j]=(0,l.useState)(!1),[b,f]=(0,l.useState)(!1),[y,g]=(0,l.useState)({cover_letter:"",resume:""}),N=async()=>{try{let s=await fetch(`/api/jobs/${e.id}`);if(s.ok){let e=await s.json();d(e.job)}else 404===s.status&&o.push("/jobs")}catch(e){console.error("Error fetching job:",e)}finally{x(!1)}},v=async a=>{if(a.preventDefault(),!s){o.push("/auth/login");return}h(!0);try{let s=await fetch("/api/applications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({job_id:parseInt(e.id),cover_letter:y.cover_letter,resume:y.resume})});if(s.ok)f(!1),g({cover_letter:"",resume:""}),N(),alert("Application submitted successfully!");else{let e=await s.json();alert(e.error||"Failed to submit application")}}catch(e){console.error("Error submitting application:",e),alert("An error occurred while submitting your application")}finally{h(!1)}},k=async()=>{if(!s){o.push("/auth/login");return}j(!0);try{let s=await fetch(`/api/bookmarks/toggle/${e.id}`,{method:"POST"});if(s.ok)N();else{let e=await s.json();alert(e.error||"Failed to bookmark job")}}catch(e){console.error("Error bookmarking job:",e),alert("An error occurred while bookmarking")}finally{j(!1)}};if(m)return t.jsx("div",{className:"min-h-screen bg-dark-darker flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("i",{className:"fas fa-spinner fa-spin text-4xl text-primary mb-4"}),t.jsx("p",{className:"text-gray-400",children:"Loading job details..."})]})});if(!c)return t.jsx("div",{className:"min-h-screen bg-dark-darker flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-warning mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Job Not Found"}),t.jsx("p",{className:"text-gray-400 mb-4",children:"The job you're looking for doesn't exist."}),t.jsx(n.default,{href:"/jobs",className:"btn-primary",children:"Browse Jobs"})]})});let w=new Date(c.deadline)<new Date,_=s?.user?.userType==="employee"&&"open"===c.status&&!w&&!c.has_applied;return(0,t.jsxs)("div",{className:"min-h-screen bg-dark-darker",children:[t.jsx("header",{className:"bg-dark border-b border-gray-700 px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(n.default,{href:"/jobs",className:"text-gray-400 hover:text-white",children:[t.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Back to Jobs"]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[s?.user?.userType==="employee"&&(0,t.jsxs)("button",{onClick:k,disabled:u,className:`btn-outline-primary ${c.is_bookmarked?"bg-primary text-white":""}`,children:[t.jsx("i",{className:`fas ${c.is_bookmarked?"fa-bookmark":"fa-bookmark-o"} mr-2`}),c.is_bookmarked?"Bookmarked":"Bookmark"]}),(0,t.jsxs)(n.default,{href:s?.user?.userType==="employee"?"/employee/dashboard":"/employer/dashboard",className:"btn-outline-primary",children:[t.jsx("i",{className:"fas fa-home mr-2"}),"Dashboard"]})]})]})}),t.jsx("div",{className:"container mx-auto px-6 py-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:c.title}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-gray-400",children:[(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-building mr-2"}),c.employer_details?.employer_profile?.company_name||c.company]}),(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-map-marker-alt mr-2"}),c.location]}),(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-clock mr-2"}),c.duration]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${"open"!==c.status||w?"bg-danger/20 text-danger":"bg-success/20 text-success"}`,children:"open"!==c.status||w?"Closed":"Open"}),c.salary&&t.jsx("p",{className:"text-lg font-semibold text-white mt-2",children:c.salary})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-400",children:[(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-users mr-2"}),c.employees_required," position",c.employees_required>1?"s":""," available"]}),(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-calendar mr-2"}),"Deadline: ",new Date(c.deadline).toLocaleDateString()]}),(0,t.jsxs)("span",{children:[t.jsx("i",{className:"fas fa-file-alt mr-2"}),c.applications_count," application",1!==c.applications_count?"s":""]})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Job Description"}),t.jsx("div",{className:"prose prose-invert max-w-none",children:t.jsx("p",{className:"text-gray-300 whitespace-pre-wrap",children:c.description})})]}),s?.user?.userType==="employee"&&t.jsx("div",{className:"mb-6",children:c.has_applied?t.jsx("div",{className:"bg-info/20 border border-info/30 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("i",{className:"fas fa-check-circle text-info text-xl mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-white",children:"Application Submitted"}),t.jsx("p",{className:"text-gray-400",children:"You have already applied for this position."})]})]})}):_?t.jsx("div",{className:"space-y-4",children:b?(0,t.jsxs)("div",{className:"bg-dark-lighter rounded-lg p-6",children:[t.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Submit Application"}),(0,t.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Cover Letter"}),t.jsx("textarea",{value:y.cover_letter,onChange:e=>g({...y,cover_letter:e.target.value}),rows:6,className:"form-input",placeholder:"Tell us why you're interested in this position..."})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Resume/CV (Optional)"}),t.jsx("textarea",{value:y.resume,onChange:e=>g({...y,resume:e.target.value}),rows:4,className:"form-input",placeholder:"Paste your resume content or provide a link..."})]}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[t.jsx("button",{type:"submit",disabled:p,className:"btn-primary flex-1",children:p?(0,t.jsxs)(t.Fragment,{children:[t.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Submitting..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx("i",{className:"fas fa-paper-plane mr-2"}),"Submit Application"]})}),t.jsx("button",{type:"button",onClick:()=>f(!1),className:"btn-outline-secondary flex-1",children:"Cancel"})]})]})]}):(0,t.jsxs)("button",{onClick:()=>f(!0),className:"btn-primary btn-lg w-full",children:[t.jsx("i",{className:"fas fa-paper-plane mr-2"}),"Apply for this Job"]})}):t.jsx("div",{className:"bg-warning/20 border border-warning/30 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("i",{className:"fas fa-exclamation-triangle text-warning text-xl mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold text-white",children:"Cannot Apply"}),t.jsx("p",{className:"text-gray-400",children:"open"!==c.status?"This job is no longer accepting applications.":w?"The application deadline has passed.":"You need to be logged in as an employee to apply."})]})]})})})]})})}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[t.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"About the Company"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium text-white",children:c.employer_details?.employer_profile?.company_name||c.company}),c.employer_details?.employer_profile?.company_description&&t.jsx("p",{className:"text-gray-400 text-sm mt-1",children:c.employer_details.employer_profile.company_description})]}),c.employer_details?.employer_profile?.location&&(0,t.jsxs)("div",{className:"flex items-center text-gray-400 text-sm",children:[t.jsx("i",{className:"fas fa-map-marker-alt mr-2"}),c.employer_details.employer_profile.location]}),c.employer_details?.employer_profile?.website&&(0,t.jsxs)("div",{className:"flex items-center text-gray-400 text-sm",children:[t.jsx("i",{className:"fas fa-globe mr-2"}),t.jsx("a",{href:c.employer_details.employer_profile.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-light",children:"Company Website"})]})]})]})}),t.jsx("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[t.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Job Statistics"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Applications"}),t.jsx("span",{className:"text-white font-medium",children:c.applications_count})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Positions"}),t.jsx("span",{className:"text-white font-medium",children:c.employees_required})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Posted"}),t.jsx("span",{className:"text-white font-medium",children:new Date(c.created_at).toLocaleDateString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Deadline"}),t.jsx("span",{className:"text-white font-medium",children:new Date(c.deadline).toLocaleDateString()})]})]})]})})]})]})})]})}},11012:(e,s,a)=>{"use strict";a.d(s,{Providers:()=>n});var t=a(10326),r=a(77109),i=a(2994),l=a(17577);function n({children:e}){let[s]=(0,l.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return t.jsx(r.SessionProvider,{children:t.jsx(i.QueryClientProvider,{client:s,children:e})})}},82955:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\jobs\[id]\page.tsx#default`)},64968:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o,metadata:()=>n});var t=a(19510),r=a(25384),i=a.n(r);a(5023);let l=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),n={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function o({children:e}){return(0,t.jsxs)("html",{lang:"en",className:"dark",children:[t.jsx("head",{children:t.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),t.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:t.jsx(l,{children:e})})]})}},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[276,539,404],()=>a(25165));module.exports=t})();