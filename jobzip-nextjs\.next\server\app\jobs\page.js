(()=>{var e={};e.id=109,e.ids=[109],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},90453:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(18485),r(64968),r(35866);var a=r(23191),t=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18485)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\jobs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\jobs\\page.tsx"],m="/jobs/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/jobs/page",pathname:"/jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2228:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},76165:(e,s,r)=>{Promise.resolve().then(r.bind(r,11012))},46407:(e,s,r)=>{Promise.resolve().then(r.bind(r,71862))},90434:(e,s,r)=>{"use strict";r.d(s,{default:()=>t.a});var a=r(79404),t=r.n(a)},35047:(e,s,r)=>{"use strict";var a=r(77389);r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}})},71862:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(10326),t=r(77109),i=r(35047),n=r(17577),l=r(90434);function o(){let{data:e,status:s}=(0,t.useSession)();(0,i.useRouter)();let[r,o]=(0,n.useState)([]),[d,c]=(0,n.useState)(!0),[m,x]=(0,n.useState)(""),[p,u]=(0,n.useState)(""),[h,b]=(0,n.useState)(1),[j,y]=(0,n.useState)(1),g=async()=>{try{let e=new URLSearchParams({page:h.toString(),limit:"12",status:"open"});m&&e.append("search",m),p&&e.append("location",p);let s=await fetch(`/api/jobs?${e}`);if(s.ok){let e=await s.json();o(e.jobs||[]),y(e.pagination?.pages||1)}}catch(e){console.error("Error fetching jobs:",e)}finally{c(!1)}},f=async s=>{if(e?.user?.userType==="employee")try{(await fetch("/api/bookmarks",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({job_id:s})})).ok&&o(r.map(e=>e.id===s?{...e,is_bookmarked:!0}:e))}catch(e){console.error("Error bookmarking job:",e)}};return"loading"===s||d?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-400",children:"Loading jobs..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-dark-darker",children:[a.jsx("header",{className:"bg-dark border-b border-gray-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-white",children:"Job Listings"}),a.jsx("p",{className:"text-gray-400",children:"Find your next opportunity"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(l.default,{href:e?.user?.userType==="employee"?"/employee/dashboard":"/employer/dashboard",className:"btn-outline-primary",children:[a.jsx("i",{className:"fas fa-home mr-2"}),"Dashboard"]}),e?.user?.userType==="employer"&&(0,a.jsxs)(l.default,{href:"/employer/jobs/new",className:"btn-primary",children:[a.jsx("i",{className:"fas fa-plus mr-2"}),"Post Job"]})]})]})}),(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("div",{className:"card mb-8",children:a.jsx("div",{className:"card-body",children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),b(1),g()},className:"flex flex-col md:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx("input",{type:"text",placeholder:"Search jobs, companies, or keywords...",value:m,onChange:e=>x(e.target.value),className:"w-full px-4 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),a.jsx("div",{className:"md:w-64",children:a.jsx("input",{type:"text",placeholder:"Location",value:p,onChange:e=>u(e.target.value),className:"w-full px-4 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,a.jsxs)("button",{type:"submit",className:"btn-primary px-6",children:[a.jsx("i",{className:"fas fa-search mr-2"}),"Search"]})]})})}),0===r.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("i",{className:"fas fa-briefcase text-6xl text-gray-600 mb-4"}),a.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"No jobs found"}),a.jsx("p",{className:"text-gray-400 mb-6",children:"Try adjusting your search criteria"}),e?.user?.userType==="employer"&&a.jsx(l.default,{href:"/employer/jobs/new",className:"btn-primary",children:"Post Your First Job"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(s=>a.jsx("div",{className:"card",children:(0,a.jsxs)("div",{className:"card-body",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:s.title}),a.jsx("p",{className:"text-primary font-medium",children:s.company})]}),e?.user?.userType==="employee"&&a.jsx("button",{onClick:()=>f(s.id),disabled:s.is_bookmarked,className:`p-2 rounded ${s.is_bookmarked?"text-warning bg-warning/20":"text-gray-400 hover:text-warning hover:bg-warning/20"}`,children:a.jsx("i",{className:`fas fa-bookmark ${s.is_bookmarked?"":"far"}`})})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-400",children:[a.jsx("i",{className:"fas fa-map-marker-alt mr-2"}),s.location]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-400",children:[a.jsx("i",{className:"fas fa-clock mr-2"}),s.duration]}),s.salary&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-success",children:[a.jsx("i",{className:"fas fa-dollar-sign mr-2"}),s.salary]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-400",children:[a.jsx("i",{className:"fas fa-users mr-2"}),s.employees_required," position",s.employees_required>1?"s":""]})]}),a.jsx("p",{className:"text-gray-300 text-sm mb-4 line-clamp-3",children:s.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsxs)("div",{children:["Posted ",new Date(s.created_at).toLocaleDateString()]}),(0,a.jsxs)("div",{children:["Deadline: ",new Date(s.deadline).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(l.default,{href:`/jobs/${s.id}`,className:"btn-outline-primary text-sm",children:"View Details"}),e?.user?.userType==="employee"&&a.jsx(l.default,{href:`/jobs/${s.id}/apply`,className:"btn-primary text-sm",children:"Apply"})]})]}),void 0!==s.applications_count&&a.jsx("div",{className:"mt-3 pt-3 border-t border-gray-700",children:(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[s.applications_count," application",1!==s.applications_count?"s":""]})})]})},s.id))}),j>1&&a.jsx("div",{className:"flex justify-center mt-8",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>b(Math.max(1,h-1)),disabled:1===h,className:"px-3 py-2 bg-dark border border-gray-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-lighter",children:"Previous"}),Array.from({length:j},(e,s)=>s+1).map(e=>a.jsx("button",{onClick:()=>b(e),className:`px-3 py-2 border border-gray-600 rounded ${h===e?"bg-primary text-white":"bg-dark text-white hover:bg-dark-lighter"}`,children:e},e)),a.jsx("button",{onClick:()=>b(Math.min(j,h+1)),disabled:h===j,className:"px-3 py-2 bg-dark border border-gray-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-lighter",children:"Next"})]})})]})]})]})}},11012:(e,s,r)=>{"use strict";r.d(s,{Providers:()=>l});var a=r(10326),t=r(77109),i=r(2994),n=r(17577);function l({children:e}){let[s]=(0,n.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return a.jsx(t.SessionProvider,{children:a.jsx(i.QueryClientProvider,{client:s,children:e})})}},18485:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\jobs\page.tsx#default`)},64968:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>l});var a=r(19510),t=r(25384),i=r.n(t);r(5023);let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),l={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function o({children:e}){return(0,a.jsxs)("html",{lang:"en",className:"dark",children:[a.jsx("head",{children:a.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),a.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:a.jsx(n,{children:e})})]})}},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[276,539,404],()=>r(90453));module.exports=a})();