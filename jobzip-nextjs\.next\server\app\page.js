(()=>{var e={};e.id=931,e.ids=[931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(24182),r(64968),r(35866);var s=r(23191),n=r(88716),i=r(37922),o=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24182)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\page.tsx"],p="/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2228:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},7586:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79404,23))},76165:(e,t,r)=>{Promise.resolve().then(r.bind(r,11012))},11012:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>a});var s=r(10326),n=r(77109),i=r(2994),o=r(17577);function a({children:e}){let[t]=(0,o.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return s.jsx(n.SessionProvider,{children:s.jsx(i.QueryClientProvider,{client:t,children:e})})}},670:(e,t,r)=>{"use strict";let{createProxy:s}=r(68570);e.exports=s("C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\node_modules\\next\\dist\\client\\link.js")},64968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var s=r(19510),n=r(25384),i=r.n(n);r(5023);let o=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),a={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function l({children:e}){return(0,s.jsxs)("html",{lang:"en",className:"dark",children:[s.jsx("head",{children:s.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),s.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:s.jsx(o,{children:e})})]})}},24182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(19510),n=r(670),i=r.n(n);function o(){return s.jsx("div",{className:"min-h-screen flex flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[s.jsx("i",{className:"fas fa-briefcase text-4xl text-primary mr-3"}),s.jsx("h1",{className:"text-5xl font-bold",children:"JobZip"})]}),s.jsx("h2",{className:"text-2xl mb-6",children:"Find Your Next Career Opportunity"}),s.jsx("p",{className:"text-lg mb-8 text-gray-300",children:"JobZip connects talented professionals with top employers. Whether you're looking for your dream job or seeking the perfect candidate, we've got you covered."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx(i(),{href:"/auth/login",className:"btn-primary text-center px-8 py-3 text-lg",children:"Log In"}),s.jsx(i(),{href:"/auth/signup",className:"btn-outline-primary text-center px-8 py-3 text-lg",children:"Sign Up"})]})]})})}},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,539,404],()=>r(2510));module.exports=s})();