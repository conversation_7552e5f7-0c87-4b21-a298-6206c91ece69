(()=>{var e={};e.id=178,e.ids=[178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14279:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>m,routeModule:()=>x,tree:()=>c}),a(85282),a(64968),a(35866);var r=a(23191),t=a(88716),i=a(37922),l=a.n(i),n=a(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(s,o);let c=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,85282)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,64968)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\Users\\<USER>\\OneDrive\\Desktop\\migration\\jobzip-nextjs\\src\\app\\profile\\page.tsx"],d="/profile/page",p={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2228:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},76165:(e,s,a)=>{Promise.resolve().then(a.bind(a,11012))},55062:(e,s,a)=>{Promise.resolve().then(a.bind(a,21144))},90434:(e,s,a)=>{"use strict";a.d(s,{default:()=>t.a});var r=a(79404),t=a.n(r)},35047:(e,s,a)=>{"use strict";var r=a(77389);a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}})},21144:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var r=a(10326),t=a(77109),i=a(35047),l=a(17577),n=a(90434);function o(){let{data:e,status:s}=(0,t.useSession)();(0,i.useRouter)();let[a,o]=(0,l.useState)(null),[c,m]=(0,l.useState)(!0),[d,p]=(0,l.useState)(!1),[x,u]=(0,l.useState)(!1),[h,f]=(0,l.useState)({}),y=async()=>{u(!0);try{let e=await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({first_name:h.first_name,last_name:h.last_name,email:h.email,location:h.location,bio:h.bio,skills:h.employee_profile?.skills,experience:h.employee_profile?.experience,education:h.employee_profile?.education,company_name:h.employer_profile?.company_name,company_description:h.employer_profile?.company_description,website:h.employer_profile?.website})});if(e.ok){let s=await e.json();o(s.user),f(s.user),p(!1),alert("Profile updated successfully!")}else{let s=await e.json();alert(s.error||"Failed to update profile")}}catch(e){console.error("Error updating profile:",e),alert("An error occurred while updating your profile")}finally{u(!1)}},j=e=>{let{name:s,value:a}=e.target;if(s.includes(".")){let[e,r]=s.split(".");f(s=>({...s,[e]:{...s[e],[r]:a}}))}else f(e=>({...e,[s]:a}))};return c?r.jsx("div",{className:"min-h-screen bg-dark-darker flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("i",{className:"fas fa-spinner fa-spin text-4xl text-primary mb-4"}),r.jsx("p",{className:"text-gray-400",children:"Loading profile..."})]})}):a?(0,r.jsxs)("div",{className:"min-h-screen bg-dark-darker",children:[r.jsx("header",{className:"bg-dark border-b border-gray-700 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-white",children:"Profile"}),r.jsx("p",{className:"text-gray-400",children:"Manage your account information"})]}),r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(n.default,{href:e?.user?.userType==="employee"?"/employee/dashboard":"/employer/dashboard",className:"btn-outline-primary",children:[r.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})})]})}),r.jsx("div",{className:"container mx-auto px-6 py-8",children:r.jsx("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"card-body text-center",children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("div",{className:"w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("i",{className:"fas fa-user text-3xl text-primary"})}),r.jsx("h2",{className:"text-xl font-bold text-white",children:a.first_name||a.last_name?`${a.first_name||""} ${a.last_name||""}`.trim():a.username}),(0,r.jsxs)("p",{className:"text-gray-400",children:["@",a.username]}),r.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-sm mt-2 ${"employee"===a.user_type?"bg-success/20 text-success":"bg-primary/20 text-primary"}`,children:"employee"===a.user_type?"Job Seeker":"Employer"})]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[r.jsx("i",{className:"fas fa-envelope mr-2"}),a.email]}),a.location&&(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[r.jsx("i",{className:"fas fa-map-marker-alt mr-2"}),a.location]}),(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[r.jsx("i",{className:"fas fa-calendar mr-2"}),"Joined ",new Date(a.date_joined).toLocaleDateString()]})]})]})})}),r.jsx("div",{className:"lg:col-span-2",children:r.jsx("div",{className:"card",children:(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h3",{className:"text-xl font-semibold text-white",children:"Profile Information"}),d?(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>{p(!1),f(a)},className:"btn-outline-secondary",children:"Cancel"}),r.jsx("button",{onClick:y,disabled:x,className:"btn-primary",children:x?(0,r.jsxs)(r.Fragment,{children:[r.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("i",{className:"fas fa-save mr-2"}),"Save Changes"]})})]}):(0,r.jsxs)("button",{onClick:()=>p(!0),className:"btn-outline-primary",children:[r.jsx("i",{className:"fas fa-edit mr-2"}),"Edit Profile"]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Basic Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"First Name"}),d?r.jsx("input",{type:"text",name:"first_name",value:h.first_name||"",onChange:j,className:"form-input"}):r.jsx("p",{className:"text-gray-400",children:a.first_name||"Not provided"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Last Name"}),d?r.jsx("input",{type:"text",name:"last_name",value:h.last_name||"",onChange:j,className:"form-input"}):r.jsx("p",{className:"text-gray-400",children:a.last_name||"Not provided"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email"}),d?r.jsx("input",{type:"email",name:"email",value:h.email||"",onChange:j,className:"form-input"}):r.jsx("p",{className:"text-gray-400",children:a.email})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Location"}),d?r.jsx("input",{type:"text",name:"location",value:h.location||"",onChange:j,className:"form-input"}):r.jsx("p",{className:"text-gray-400",children:a.location||"Not provided"})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),d?r.jsx("textarea",{name:"bio",value:h.bio||"",onChange:j,rows:3,className:"form-input",placeholder:"Tell us about yourself..."}):r.jsx("p",{className:"text-gray-400",children:a.bio||"No bio provided"})]})]}),"employee"===a.user_type&&(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Professional Information"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Skills"}),d?r.jsx("textarea",{name:"employee_profile.skills",value:h.employee_profile?.skills||"",onChange:j,rows:3,className:"form-input",placeholder:"List your skills and technologies..."}):r.jsx("p",{className:"text-gray-400",children:a.employee_profile?.skills||"No skills listed"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Experience"}),d?r.jsx("textarea",{name:"employee_profile.experience",value:h.employee_profile?.experience||"",onChange:j,rows:4,className:"form-input",placeholder:"Describe your work experience..."}):r.jsx("p",{className:"text-gray-400",children:a.employee_profile?.experience||"No experience listed"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Education"}),d?r.jsx("textarea",{name:"employee_profile.education",value:h.employee_profile?.education||"",onChange:j,rows:3,className:"form-input",placeholder:"List your educational background..."}):r.jsx("p",{className:"text-gray-400",children:a.employee_profile?.education||"No education listed"})]})]})]}),"employer"===a.user_type&&(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Company Information"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Company Name"}),d?r.jsx("input",{type:"text",name:"employer_profile.company_name",value:h.employer_profile?.company_name||"",onChange:j,className:"form-input"}):r.jsx("p",{className:"text-gray-400",children:a.employer_profile?.company_name||"Not provided"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Company Description"}),d?r.jsx("textarea",{name:"employer_profile.company_description",value:h.employer_profile?.company_description||"",onChange:j,rows:4,className:"form-input",placeholder:"Describe your company..."}):r.jsx("p",{className:"text-gray-400",children:a.employer_profile?.company_description||"No description provided"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Website"}),d?r.jsx("input",{type:"url",name:"employer_profile.website",value:h.employer_profile?.website||"",onChange:j,className:"form-input",placeholder:"https://..."}):r.jsx("p",{className:"text-gray-400",children:a.employer_profile?.website?r.jsx("a",{href:a.employer_profile.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-light",children:a.employer_profile.website}):"No website provided"})]})]})]})]})]})})})]})})})]}):r.jsx("div",{className:"min-h-screen bg-dark-darker flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-warning mb-4"}),r.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Profile Not Found"}),r.jsx("p",{className:"text-gray-400 mb-4",children:"Unable to load your profile."}),r.jsx(n.default,{href:"/",className:"btn-primary",children:"Go Home"})]})})}},11012:(e,s,a)=>{"use strict";a.d(s,{Providers:()=>n});var r=a(10326),t=a(77109),i=a(2994),l=a(17577);function n({children:e}){let[s]=(0,l.useState)(()=>new i.QueryClient({defaultOptions:{queries:{staleTime:6e4,cacheTime:6e5}}}));return r.jsx(t.SessionProvider,{children:r.jsx(i.QueryClientProvider,{client:s,children:e})})}},64968:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o,metadata:()=>n});var r=a(19510),t=a(25384),i=a.n(t);a(5023);let l=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`),n={title:"JobZip - Find Your Next Career Opportunity",description:"JobZip helps you find and apply to the best jobs in your field."};function o({children:e}){return(0,r.jsxs)("html",{lang:"en",className:"dark",children:[r.jsx("head",{children:r.jsx("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"})}),r.jsx("body",{className:`${i().className} bg-dark-darker text-white min-h-screen`,children:r.jsx(l,{children:e})})]})}},85282:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\profile\page.tsx#default`)},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[276,539,404],()=>a(14279));module.exports=r})();