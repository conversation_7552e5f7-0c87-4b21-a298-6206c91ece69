{"name": "jobzip-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.11.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.6.5", "bcryptjs": "^3.0.2", "clsx": "^2.1.0", "date-fns": "^3.2.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "next": "^14.0.4", "next-auth": "^4.24.5", "postcss": "^8.4.33", "prisma": "^6.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-query": "^3.39.3", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.11"}}