// JobZip Prisma Schema
// Migrated from Django models to match exact structure

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model - extends Django's AbstractUser
model User {
  id            Int      @id @default(autoincrement())
  username      String   @unique
  email         String   @unique
  first_name    String?
  last_name     String?
  password      String
  user_type     String   @default("employee") // 'employee' or 'employer'
  profile_picture String?
  location      String?
  bio           String?
  date_joined   DateTime @default(now())
  is_active     Boolean  @default(true)
  is_staff      Boolean  @default(false)
  is_superuser  <PERSON>olean  @default(false)
  last_login    DateTime?

  // Relations
  posted_jobs         Job[]           @relation("EmployerJobs")
  job_applications    JobApplication[] @relation("ApplicantApplications")
  job_enrollments     JobEnrollment[] @relation("EmployeeEnrollments")
  job_reviews         JobReview[]     @relation("EmployeeJobReviews")
  employee_reviews    EmployeeReview[] @relation("EmployeeReviews")
  given_reviews       EmployeeReview[] @relation("EmployerReviews")
  bookmarks           Bookmark[]
  notifications       Notification[]
  reports_filed       Report[]        @relation("ReporterReports")
  reports_received    Report[]        @relation("ReportedUserReports")
  comments            Comment[]
  liked_comments      Comment[]       @relation("CommentLikes")
  disliked_comments   Comment[]       @relation("CommentDislikes")
  employer_profile    EmployerProfile?
  employee_profile    EmployeeProfile?

  @@map("auth_user")
}

// Job model
model Job {
  id                 Int      @id @default(autoincrement())
  employer_id        Int
  title              String
  description        String
  location           String
  duration           String
  company            String
  salary             String?
  employees_required Int
  deadline           DateTime
  created_at         DateTime @default(now())
  status             String   @default("open") // 'open' or 'closed'

  // Relations
  employer         User              @relation("EmployerJobs", fields: [employer_id], references: [id], onDelete: Cascade)
  applications     JobApplication[]
  enrollments      JobEnrollment[]
  reviews          JobReview[]
  employee_reviews EmployeeReview[]
  bookmarks        Bookmark[]
  company_pictures CompanyPicture[]

  @@map("jobs_job")
}

// Company Picture model
model CompanyPicture {
  id          Int      @id @default(autoincrement())
  image       String
  uploaded_at DateTime @default(now())
  job_id      Int

  // Relations
  job Job @relation(fields: [job_id], references: [id], onDelete: Cascade)

  @@map("jobs_companypicture")
}

// Job Application model
model JobApplication {
  id           Int      @id @default(autoincrement())
  job_id       Int
  applicant_id Int
  status       String   @default("pending") // 'pending', 'accepted', 'rejected'
  cover_letter String?
  resume       String?
  applied_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  job       Job  @relation(fields: [job_id], references: [id], onDelete: Cascade)
  applicant User @relation("ApplicantApplications", fields: [applicant_id], references: [id], onDelete: Cascade)

  @@unique([job_id, applicant_id])
  @@map("jobs_jobapplication")
}

// Job Enrollment model
model JobEnrollment {
  id          Int      @id @default(autoincrement())
  job_id      Int
  employee_id Int
  status      String   @default("pending")
  enrolled_at DateTime @default(now())

  // Relations
  job      Job  @relation(fields: [job_id], references: [id], onDelete: Cascade)
  employee User @relation("EmployeeEnrollments", fields: [employee_id], references: [id], onDelete: Cascade)

  @@map("jobs_jobenrollment")
}

// Job Review model
model JobReview {
  id          Int      @id @default(autoincrement())
  job_id      Int
  employee_id Int
  rating      Int
  comment     String
  created_at  DateTime @default(now())

  // Relations
  job      Job       @relation(fields: [job_id], references: [id], onDelete: Cascade)
  employee User      @relation("EmployeeJobReviews", fields: [employee_id], references: [id], onDelete: Cascade)
  comments Comment[]

  @@map("jobs_jobreview")
}

// Employee Review model
model EmployeeReview {
  id          Int      @id @default(autoincrement())
  job_id      Int
  employee_id Int
  employer_id Int
  rating      Int
  remarks     String
  created_at  DateTime @default(now())

  // Relations
  job      Job  @relation(fields: [job_id], references: [id], onDelete: Cascade)
  employee User @relation("EmployeeReviews", fields: [employee_id], references: [id], onDelete: Cascade)
  employer User @relation("EmployerReviews", fields: [employer_id], references: [id], onDelete: Cascade)

  @@map("jobs_employeereview")
}

// Bookmark model
model Bookmark {
  id         Int      @id @default(autoincrement())
  user_id    Int
  job_id     Int
  created_at DateTime @default(now())

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  job  Job  @relation(fields: [job_id], references: [id], onDelete: Cascade)

  @@map("jobs_bookmark")
}

// Notification model
model Notification {
  id                Int      @id @default(autoincrement())
  user_id           Int
  notification_type String   // 'listing', 'bookmark', 'review', 'current_job', 'general'
  message           String
  is_read           Boolean  @default(false)
  created_at        DateTime @default(now())

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("jobs_notification")
}

// Report model
model Report {
  id               Int      @id @default(autoincrement())
  reporter_id      Int
  reported_user_id Int
  reason           String
  created_at       DateTime @default(now())
  status           String   @default("pending")
  ban_duration     Int?     // Duration in days

  // Relations
  reporter      User @relation("ReporterReports", fields: [reporter_id], references: [id], onDelete: Cascade)
  reported_user User @relation("ReportedUserReports", fields: [reported_user_id], references: [id], onDelete: Cascade)

  @@map("jobs_report")
}

// Comment model
model Comment {
  id         Int      @id @default(autoincrement())
  review_id  Int
  user_id    Int
  content    String
  parent_id  Int?
  created_at DateTime @default(now())

  // Relations
  review   JobReview @relation(fields: [review_id], references: [id], onDelete: Cascade)
  user     User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parent_id], references: [id])
  replies  Comment[] @relation("CommentReplies")
  likes    User[]    @relation("CommentLikes")
  dislikes User[]    @relation("CommentDislikes")

  @@map("jobs_comment")
}

// Employer Profile model
model EmployerProfile {
  id                  Int      @id @default(autoincrement())
  user_id             Int      @unique
  company_name        String?
  company_description String?
  website             String?
  location            String?
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("jobs_employerprofile")
}

// Employee Profile model
model EmployeeProfile {
  id         Int      @id @default(autoincrement())
  user_id    Int      @unique
  skills     String?
  experience String?
  education  String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("jobs_employeeprofile")
}
