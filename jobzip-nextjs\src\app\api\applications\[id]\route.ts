import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateApplicationSchema = z.object({
  status: z.enum(['pending', 'accepted', 'rejected']).optional(),
  cover_letter: z.string().optional(),
  resume: z.string().optional(),
});

// GET /api/applications/[id] - Get application details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const applicationId = parseInt(params.id);
    
    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    const application = await prisma.jobApplication.findUnique({
      where: { id: applicationId },
      include: {
        job: {
          include: {
            employer: {
              select: {
                id: true,
                username: true,
                employer_profile: {
                  select: {
                    company_name: true,
                  },
                },
              },
            },
          },
        },
        applicant: {
          select: {
            id: true,
            username: true,
            email: true,
            first_name: true,
            last_name: true,
            employee_profile: {
              select: {
                skills: true,
                experience: true,
                education: true,
              },
            },
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Check authorization - only applicant or job employer can view
    const isApplicant = session.user.userType === 'employee' && 
                       application.applicant_id === parseInt(session.user.id);
    const isEmployer = session.user.userType === 'employer' && 
                      application.job.employer_id === parseInt(session.user.id);

    if (!isApplicant && !isEmployer) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Transform the data to match our interface
    const transformedApplication = {
      ...application,
      job_details: application.job,
      applicant_details: application.applicant,
    };

    return NextResponse.json({ application: transformedApplication });
  } catch (error) {
    console.error('Error fetching application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/applications/[id] - Update application (status change by employer or edit by applicant)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const applicationId = parseInt(params.id);
    
    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    // Check if application exists
    const existingApplication = await prisma.jobApplication.findUnique({
      where: { id: applicationId },
      include: {
        job: true,
      },
    });

    if (!existingApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const validatedData = updateApplicationSchema.parse(body);

    // Check authorization based on what's being updated
    if (validatedData.status) {
      // Only employer can change status
      if (session.user.userType !== 'employer' || 
          existingApplication.job.employer_id !== parseInt(session.user.id)) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        );
      }
    } else {
      // Only applicant can edit cover letter/resume
      if (session.user.userType !== 'employee' || 
          existingApplication.applicant_id !== parseInt(session.user.id)) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        );
      }
    }

    const updatedApplication = await prisma.jobApplication.update({
      where: { id: applicationId },
      data: validatedData,
      include: {
        job: {
          include: {
            employer: {
              select: {
                id: true,
                username: true,
                employer_profile: {
                  select: {
                    company_name: true,
                  },
                },
              },
            },
          },
        },
        applicant: {
          select: {
            id: true,
            username: true,
            email: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Create notification if status was changed
    if (validatedData.status) {
      await prisma.notification.create({
        data: {
          user_id: existingApplication.applicant_id,
          notification_type: 'application',
          message: `Your application for "${existingApplication.job.title}" has been ${validatedData.status}`,
        },
      });
    }

    return NextResponse.json({
      message: 'Application updated successfully',
      application: {
        ...updatedApplication,
        job_details: updatedApplication.job,
        applicant_details: updatedApplication.applicant,
      },
    });
  } catch (error) {
    console.error('Error updating application:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/applications/[id] - Delete application (withdraw)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employee') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const applicationId = parseInt(params.id);
    
    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    // Check if application exists and belongs to the user
    const existingApplication = await prisma.jobApplication.findUnique({
      where: { id: applicationId },
    });

    if (!existingApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    if (existingApplication.applicant_id !== parseInt(session.user.id)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    await prisma.jobApplication.delete({
      where: { id: applicationId },
    });

    return NextResponse.json({
      message: 'Application withdrawn successfully',
    });
  } catch (error) {
    console.error('Error deleting application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
