import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

// GET /api/applications/received - List applications received by employer
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employer') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const jobId = searchParams.get('job_id');

    const skip = (page - 1) * limit;

    // Build where clause for applications to employer's jobs
    const where: any = {
      job: {
        employer_id: parseInt(session.user.id),
      },
    };
    
    if (status) {
      where.status = status;
    }

    if (jobId) {
      where.job_id = parseInt(jobId);
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
              status: true,
            },
          },
          applicant: {
            select: {
              id: true,
              username: true,
              email: true,
              first_name: true,
              last_name: true,
              employee_profile: {
                select: {
                  skills: true,
                  experience: true,
                  education: true,
                },
              },
            },
          },
        },
        orderBy: {
          applied_at: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.jobApplication.count({ where }),
    ]);

    // Transform the data to match our interface
    const transformedApplications = applications.map(application => ({
      ...application,
      job_details: application.job,
      applicant_details: application.applicant,
    }));

    return NextResponse.json({
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching received applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
