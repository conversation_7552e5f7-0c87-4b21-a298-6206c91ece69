import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const createApplicationSchema = z.object({
  job_id: z.number(),
  cover_letter: z.string().optional(),
  resume: z.string().optional(),
});

// GET /api/applications - List user's applications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const skip = (page - 1) * limit;

    // Build where clause based on user type
    const where: any = {};
    
    if (session.user.userType === 'employee') {
      where.applicant_id = parseInt(session.user.id);
    } else {
      // For employers, show applications to their jobs
      where.job = {
        employer_id: parseInt(session.user.id),
      };
    }
    
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where,
        include: {
          job: {
            include: {
              employer: {
                select: {
                  id: true,
                  username: true,
                  employer_profile: {
                    select: {
                      company_name: true,
                    },
                  },
                },
              },
            },
          },
          applicant: {
            select: {
              id: true,
              username: true,
              email: true,
              first_name: true,
              last_name: true,
              employee_profile: true,
            },
          },
        },
        orderBy: {
          applied_at: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.jobApplication.count({ where }),
    ]);

    // Transform the data to match our interface
    const transformedApplications = applications.map(app => ({
      ...app,
      job_details: app.job,
      applicant_details: app.applicant,
    }));

    return NextResponse.json({
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/applications - Create a new job application
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employee') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createApplicationSchema.parse(body);

    // Check if job exists and is open
    const job = await prisma.job.findUnique({
      where: { id: validatedData.job_id },
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    if (job.status !== 'open') {
      return NextResponse.json(
        { error: 'Job is no longer accepting applications' },
        { status: 400 }
      );
    }

    // Check if user already applied
    const existingApplication = await prisma.jobApplication.findUnique({
      where: {
        job_id_applicant_id: {
          job_id: validatedData.job_id,
          applicant_id: parseInt(session.user.id),
        },
      },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied to this job' },
        { status: 400 }
      );
    }

    const application = await prisma.jobApplication.create({
      data: {
        ...validatedData,
        applicant_id: parseInt(session.user.id),
      },
      include: {
        job: {
          include: {
            employer: {
              select: {
                id: true,
                username: true,
                employer_profile: {
                  select: {
                    company_name: true,
                  },
                },
              },
            },
          },
        },
        applicant: {
          select: {
            id: true,
            username: true,
            email: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Create notification for employer
    await prisma.notification.create({
      data: {
        user_id: job.employer_id,
        notification_type: 'listing',
        message: `${session.user.username} has applied to your job: ${job.title}`,
      },
    });

    return NextResponse.json({
      message: 'Application submitted successfully',
      application: {
        ...application,
        job_details: application.job,
        applicant_details: application.applicant,
      },
    });
  } catch (error) {
    console.error('Error creating application:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
