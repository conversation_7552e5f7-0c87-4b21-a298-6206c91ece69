import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const createBookmarkSchema = z.object({
  job_id: z.number(),
});

// GET /api/bookmarks - List user's bookmarked jobs
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employee') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    const [bookmarks, total] = await Promise.all([
      prisma.bookmark.findMany({
        where: {
          user_id: parseInt(session.user.id),
        },
        include: {
          job: {
            include: {
              employer: {
                select: {
                  id: true,
                  username: true,
                  employer_profile: {
                    select: {
                      company_name: true,
                    },
                  },
                },
              },
              _count: {
                select: {
                  applications: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.bookmark.count({
        where: {
          user_id: parseInt(session.user.id),
        },
      }),
    ]);

    // Transform the data to match our interface
    const jobs = bookmarks.map(bookmark => ({
      ...bookmark.job,
      employer_details: bookmark.job.employer,
      applications_count: bookmark.job._count.applications,
      is_bookmarked: true,
    }));

    return NextResponse.json({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching bookmarks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/bookmarks - Add a job to bookmarks
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employee') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createBookmarkSchema.parse(body);

    // Check if job exists
    const job = await prisma.job.findUnique({
      where: { id: validatedData.job_id },
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Check if already bookmarked
    const existingBookmark = await prisma.bookmark.findFirst({
      where: {
        user_id: parseInt(session.user.id),
        job_id: validatedData.job_id,
      },
    });

    if (existingBookmark) {
      return NextResponse.json(
        { error: 'Job already bookmarked' },
        { status: 400 }
      );
    }

    const bookmark = await prisma.bookmark.create({
      data: {
        user_id: parseInt(session.user.id),
        job_id: validatedData.job_id,
      },
      include: {
        job: {
          include: {
            employer: {
              select: {
                id: true,
                username: true,
                employer_profile: {
                  select: {
                    company_name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Create notification for user
    await prisma.notification.create({
      data: {
        user_id: parseInt(session.user.id),
        notification_type: 'bookmark',
        message: `You bookmarked the job: ${job.title}`,
      },
    });

    return NextResponse.json({
      message: 'Job bookmarked successfully',
      bookmark: {
        ...bookmark,
        job_details: bookmark.job,
      },
    });
  } catch (error) {
    console.error('Error creating bookmark:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/bookmarks - Remove a job from bookmarks
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employee') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const jobId = parseInt(searchParams.get('job_id') || '0');

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const bookmark = await prisma.bookmark.findFirst({
      where: {
        user_id: parseInt(session.user.id),
        job_id: jobId,
      },
    });

    if (!bookmark) {
      return NextResponse.json(
        { error: 'Bookmark not found' },
        { status: 404 }
      );
    }

    await prisma.bookmark.delete({
      where: {
        id: bookmark.id,
      },
    });

    return NextResponse.json({
      message: 'Bookmark removed successfully',
    });
  } catch (error) {
    console.error('Error removing bookmark:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
