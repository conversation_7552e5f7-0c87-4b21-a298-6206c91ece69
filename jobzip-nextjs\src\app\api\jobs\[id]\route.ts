import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateJobSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).optional(),
  location: z.string().min(1).max(100).optional(),
  duration: z.string().min(1).max(50).optional(),
  company: z.string().min(1).max(200).optional(),
  salary: z.string().optional(),
  employees_required: z.number().min(1).optional(),
  deadline: z.string().datetime().optional(),
  status: z.enum(['open', 'closed']).optional(),
});

// GET /api/jobs/[id] - Get job details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const jobId = parseInt(params.id);
    
    if (isNaN(jobId)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      );
    }

    const job = await prisma.job.findUnique({
      where: { id: jobId },
      include: {
        employer: {
          select: {
            id: true,
            username: true,
            employer_profile: {
              select: {
                company_name: true,
                company_description: true,
                website: true,
                location: true,
              },
            },
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Check if current user has applied (if logged in as employee)
    const session = await getServerSession(authOptions);
    let hasApplied = false;
    let isBookmarked = false;

    if (session && session.user.userType === 'employee') {
      const application = await prisma.jobApplication.findUnique({
        where: {
          job_id_applicant_id: {
            job_id: jobId,
            applicant_id: parseInt(session.user.id),
          },
        },
      });
      hasApplied = !!application;

      const bookmark = await prisma.bookmark.findFirst({
        where: {
          user_id: parseInt(session.user.id),
          job_id: jobId,
        },
      });
      isBookmarked = !!bookmark;
    }

    // Transform the data to match our interface
    const transformedJob = {
      ...job,
      employer_details: job.employer,
      applications_count: job._count.applications,
      has_applied: hasApplied,
      is_bookmarked: isBookmarked,
    };

    return NextResponse.json({ job: transformedJob });
  } catch (error) {
    console.error('Error fetching job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/jobs/[id] - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employer') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = parseInt(params.id);
    
    if (isNaN(jobId)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      );
    }

    // Check if job exists and belongs to the employer
    const existingJob = await prisma.job.findUnique({
      where: { id: jobId },
    });

    if (!existingJob) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    if (existingJob.employer_id !== parseInt(session.user.id)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = updateJobSchema.parse(body);

    // Prepare update data
    const updateData: any = { ...validatedData };
    if (validatedData.deadline) {
      updateData.deadline = new Date(validatedData.deadline);
    }

    const updatedJob = await prisma.job.update({
      where: { id: jobId },
      data: updateData,
      include: {
        employer: {
          select: {
            id: true,
            username: true,
            employer_profile: {
              select: {
                company_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Job updated successfully',
      job: {
        ...updatedJob,
        employer_details: updatedJob.employer,
        applications_count: updatedJob._count.applications,
      },
    });
  } catch (error) {
    console.error('Error updating job:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/jobs/[id] - Delete job
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.userType !== 'employer') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = parseInt(params.id);
    
    if (isNaN(jobId)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      );
    }

    // Check if job exists and belongs to the employer
    const existingJob = await prisma.job.findUnique({
      where: { id: jobId },
    });

    if (!existingJob) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    if (existingJob.employer_id !== parseInt(session.user.id)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    await prisma.job.delete({
      where: { id: jobId },
    });

    return NextResponse.json({
      message: 'Job deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
