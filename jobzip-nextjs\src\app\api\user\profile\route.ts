import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import bcrypt from 'bcryptjs';

const updateProfileSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().email().optional(),
  location: z.string().optional(),
  bio: z.string().optional(),
  // Employee specific fields
  skills: z.string().optional(),
  experience: z.string().optional(),
  education: z.string().optional(),
  // Employer specific fields
  company_name: z.string().optional(),
  company_description: z.string().optional(),
  website: z.string().optional(),
});

const changePasswordSchema = z.object({
  current_password: z.string().min(1),
  new_password: z.string().min(6),
});

// GET /api/user/profile - Get user profile
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id) },
      select: {
        id: true,
        username: true,
        email: true,
        first_name: true,
        last_name: true,
        user_type: true,
        location: true,
        bio: true,
        profile_picture: true,
        date_joined: true,
        last_login: true,
        employee_profile: {
          select: {
            skills: true,
            experience: true,
            education: true,
          },
        },
        employer_profile: {
          select: {
            company_name: true,
            company_description: true,
            website: true,
            location: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/user/profile - Update user profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateProfileSchema.parse(body);

    const userId = parseInt(session.user.id);

    // Separate user fields from profile fields
    const userFields: any = {};
    const profileFields: any = {};

    // User table fields
    if (validatedData.first_name !== undefined) userFields.first_name = validatedData.first_name;
    if (validatedData.last_name !== undefined) userFields.last_name = validatedData.last_name;
    if (validatedData.email !== undefined) userFields.email = validatedData.email;
    if (validatedData.location !== undefined) userFields.location = validatedData.location;
    if (validatedData.bio !== undefined) userFields.bio = validatedData.bio;

    // Profile table fields
    if (session.user.userType === 'employee') {
      if (validatedData.skills !== undefined) profileFields.skills = validatedData.skills;
      if (validatedData.experience !== undefined) profileFields.experience = validatedData.experience;
      if (validatedData.education !== undefined) profileFields.education = validatedData.education;
    } else if (session.user.userType === 'employer') {
      if (validatedData.company_name !== undefined) profileFields.company_name = validatedData.company_name;
      if (validatedData.company_description !== undefined) profileFields.company_description = validatedData.company_description;
      if (validatedData.website !== undefined) profileFields.website = validatedData.website;
      if (validatedData.location !== undefined) profileFields.location = validatedData.location;
    }

    // Update user table
    if (Object.keys(userFields).length > 0) {
      await prisma.user.update({
        where: { id: userId },
        data: userFields,
      });
    }

    // Update profile table
    if (Object.keys(profileFields).length > 0) {
      if (session.user.userType === 'employee') {
        await prisma.employeeProfile.upsert({
          where: { user_id: userId },
          update: profileFields,
          create: {
            user_id: userId,
            ...profileFields,
          },
        });
      } else if (session.user.userType === 'employer') {
        await prisma.employerProfile.upsert({
          where: { user_id: userId },
          update: profileFields,
          create: {
            user_id: userId,
            ...profileFields,
          },
        });
      }
    }

    // Fetch updated user data
    const updatedUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        first_name: true,
        last_name: true,
        user_type: true,
        location: true,
        bio: true,
        profile_picture: true,
        date_joined: true,
        last_login: true,
        employee_profile: {
          select: {
            skills: true,
            experience: true,
            education: true,
          },
        },
        employer_profile: {
          select: {
            company_name: true,
            company_description: true,
            website: true,
            location: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Profile updated successfully',
      user: updatedUser,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/user/profile - Change password
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = changePasswordSchema.parse(body);

    const userId = parseInt(session.user.id);

    // Get current user with password
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        password: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(
      validatedData.current_password,
      user.password
    );

    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      );
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(validatedData.new_password, 12);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });

    return NextResponse.json({
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Error changing password:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
