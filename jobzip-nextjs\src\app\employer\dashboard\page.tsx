'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job, JobApplication } from '@/types';
import { NotificationBell } from '@/components/notifications';

export default function EmployerDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeJobs, setActiveJobs] = useState<Job[]>([]);
  const [recentApplications, setRecentApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employer') {
      router.push('/employee/dashboard');
      return;
    }

    fetchDashboardData();
  }, [session, status, router]);

  const fetchDashboardData = async () => {
    try {
      // Fetch active jobs
      const jobsRes = await fetch('/api/jobs/my-jobs?status=open');
      if (jobsRes.ok) {
        const jobsData = await jobsRes.json();
        setActiveJobs(jobsData.jobs || []);
      }

      // Fetch recent applications
      const applicationsRes = await fetch('/api/applications/received?limit=10');
      if (applicationsRes.ok) {
        const applicationsData = await applicationsRes.json();
        setRecentApplications(applicationsData.applications || []);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Welcome back, {session?.user?.name}!</h1>
            <p className="text-gray-400">Manage your job postings and applications</p>
          </div>
          <div className="flex items-center space-x-4">
            <NotificationBell />
            <Link href="/profile" className="btn-outline-primary">
              <i className="fas fa-building mr-2"></i>
              Company Profile
            </Link>
            <Link href="/employer/jobs/new" className="btn-primary">
              <i className="fas fa-plus mr-2"></i>
              Post Job
            </Link>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-primary/20 rounded-full">
                  <i className="fas fa-briefcase text-primary text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Active Jobs</p>
                  <p className="text-2xl font-bold text-white">{activeJobs.length}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-info/20 rounded-full">
                  <i className="fas fa-users text-info text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Total Applications</p>
                  <p className="text-2xl font-bold text-white">{recentApplications.length}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-warning/20 rounded-full">
                  <i className="fas fa-clock text-warning text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Pending Reviews</p>
                  <p className="text-2xl font-bold text-white">
                    {recentApplications.filter(app => app.status === 'pending').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-success/20 rounded-full">
                  <i className="fas fa-check text-success text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Hired</p>
                  <p className="text-2xl font-bold text-white">
                    {recentApplications.filter(app => app.status === 'accepted').length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Active Jobs */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Active Job Postings</h2>
                <Link href="/employer/jobs" className="text-primary hover:text-primary-light">
                  View All
                </Link>
              </div>

              {activeJobs.length === 0 ? (
                <div className="text-center py-8">
                  <i className="fas fa-briefcase text-4xl text-gray-600 mb-4"></i>
                  <p className="text-gray-400">No active job postings</p>
                  <Link href="/employer/jobs/new" className="btn-primary mt-4">
                    Post Your First Job
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeJobs.slice(0, 5).map((job) => (
                    <div key={job.id} className="p-3 bg-dark-lighter rounded">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-white">{job.title}</h3>
                          <p className="text-sm text-gray-400">{job.location}</p>
                          <p className="text-xs text-gray-500">
                            Posted {new Date(job.created_at).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-gray-500">
                            Deadline: {new Date(job.deadline).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-white">
                            {job.applications_count || 0} applications
                          </p>
                          <div className="flex space-x-2 mt-2">
                            <Link
                              href={`/employer/jobs/${job.id}`}
                              className="btn-outline-primary text-xs"
                            >
                              View
                            </Link>
                            <Link
                              href={`/employer/jobs/${job.id}/applications`}
                              className="btn-primary text-xs"
                            >
                              Applications
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Recent Applications */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Recent Applications</h2>
                <Link href="/employer/applications" className="text-primary hover:text-primary-light">
                  View All
                </Link>
              </div>

              {recentApplications.length === 0 ? (
                <div className="text-center py-8">
                  <i className="fas fa-file-alt text-4xl text-gray-600 mb-4"></i>
                  <p className="text-gray-400">No applications yet</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Applications will appear here when candidates apply to your jobs
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentApplications.slice(0, 8).map((application) => (
                    <div key={application.id} className="flex items-center justify-between p-3 bg-dark-lighter rounded">
                      <div className="flex-1">
                        <h3 className="font-medium text-white">{application.applicant_details?.username}</h3>
                        <p className="text-sm text-gray-400">{application.job_details?.title}</p>
                        <p className="text-xs text-gray-500">
                          Applied {new Date(application.applied_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          application.status === 'pending' ? 'bg-warning/20 text-warning' :
                          application.status === 'accepted' ? 'bg-success/20 text-success' :
                          'bg-danger/20 text-danger'
                        }`}>
                          {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                        </span>
                        <Link
                          href={`/employer/applications/${application.id}`}
                          className="btn-outline-primary text-xs"
                        >
                          Review
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="card">
            <div className="card-body">
              <h2 className="text-xl font-semibold text-white mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link href="/employer/jobs/new" className="p-4 bg-dark-lighter rounded hover:bg-dark transition-colors">
                  <div className="flex items-center">
                    <div className="p-3 bg-primary/20 rounded-full">
                      <i className="fas fa-plus text-primary text-xl"></i>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-medium text-white">Post New Job</h3>
                      <p className="text-sm text-gray-400">Create a new job posting</p>
                    </div>
                  </div>
                </Link>

                <Link href="/employer/applications" className="p-4 bg-dark-lighter rounded hover:bg-dark transition-colors">
                  <div className="flex items-center">
                    <div className="p-3 bg-info/20 rounded-full">
                      <i className="fas fa-users text-info text-xl"></i>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-medium text-white">Review Applications</h3>
                      <p className="text-sm text-gray-400">Manage candidate applications</p>
                    </div>
                  </div>
                </Link>

                <Link href="/employer/analytics" className="p-4 bg-dark-lighter rounded hover:bg-dark transition-colors">
                  <div className="flex items-center">
                    <div className="p-3 bg-success/20 rounded-full">
                      <i className="fas fa-chart-bar text-success text-xl"></i>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-medium text-white">View Analytics</h3>
                      <p className="text-sm text-gray-400">Track your hiring metrics</p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
