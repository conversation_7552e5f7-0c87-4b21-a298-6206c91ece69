'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function NewJobPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    duration: '',
    company: '',
    salary: '',
    employees_required: 1,
    deadline: '',
  });

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employer') {
      router.push('/employee/dashboard');
      return;
    }
  }, [session, status, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          employees_required: parseInt(formData.employees_required.toString()),
          deadline: new Date(formData.deadline).toISOString(),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        router.push(`/jobs/${data.job.id}`);
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Failed to create job posting');
      }
    } catch (error) {
      console.error('Error creating job:', error);
      alert('An error occurred while creating the job posting');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session || session.user.userType !== 'employer') {
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Post a New Job</h1>
            <p className="text-gray-400">Create a job posting to find the perfect candidates</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/employer/dashboard" className="btn-outline-primary">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="card">
            <div className="card-body">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h2 className="text-xl font-semibold text-white mb-4">Basic Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-2">
                        Job Title *
                      </label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        required
                        className="form-input"
                        placeholder="e.g. Senior Software Engineer"
                      />
                    </div>

                    <div>
                      <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                        required
                        className="form-input"
                        placeholder="e.g. TechCorp Inc."
                      />
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-2">
                        Location *
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleChange}
                        required
                        className="form-input"
                        placeholder="e.g. New York, NY or Remote"
                      />
                    </div>

                    <div>
                      <label htmlFor="duration" className="block text-sm font-medium text-gray-300 mb-2">
                        Duration *
                      </label>
                      <select
                        id="duration"
                        name="duration"
                        value={formData.duration}
                        onChange={handleChange}
                        required
                        className="form-input"
                      >
                        <option value="">Select duration</option>
                        <option value="Full-time">Full-time</option>
                        <option value="Part-time">Part-time</option>
                        <option value="Contract">Contract</option>
                        <option value="Temporary">Temporary</option>
                        <option value="Internship">Internship</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="salary" className="block text-sm font-medium text-gray-300 mb-2">
                        Salary (Optional)
                      </label>
                      <input
                        type="text"
                        id="salary"
                        name="salary"
                        value={formData.salary}
                        onChange={handleChange}
                        className="form-input"
                        placeholder="e.g. $80,000 - $120,000 per year"
                      />
                    </div>

                    <div>
                      <label htmlFor="employees_required" className="block text-sm font-medium text-gray-300 mb-2">
                        Number of Positions *
                      </label>
                      <input
                        type="number"
                        id="employees_required"
                        name="employees_required"
                        value={formData.employees_required}
                        onChange={handleChange}
                        required
                        min="1"
                        className="form-input"
                      />
                    </div>
                  </div>
                </div>

                {/* Job Description */}
                <div>
                  <h2 className="text-xl font-semibold text-white mb-4">Job Description</h2>
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      required
                      rows={10}
                      className="form-input"
                      placeholder="Provide a detailed description of the job role, responsibilities, requirements, and qualifications..."
                    />
                    <p className="text-sm text-gray-400 mt-2">
                      Include job responsibilities, required skills, qualifications, and any other relevant information.
                    </p>
                  </div>
                </div>

                {/* Application Details */}
                <div>
                  <h2 className="text-xl font-semibold text-white mb-4">Application Details</h2>
                  <div>
                    <label htmlFor="deadline" className="block text-sm font-medium text-gray-300 mb-2">
                      Application Deadline *
                    </label>
                    <input
                      type="datetime-local"
                      id="deadline"
                      name="deadline"
                      value={formData.deadline}
                      onChange={handleChange}
                      required
                      min={new Date().toISOString().slice(0, 16)}
                      className="form-input"
                    />
                    <p className="text-sm text-gray-400 mt-2">
                      Set the last date and time when applications will be accepted.
                    </p>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-700">
                  <Link href="/employer/dashboard" className="btn-outline-secondary">
                    <i className="fas fa-times mr-2"></i>
                    Cancel
                  </Link>
                  
                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => {
                        // Save as draft functionality could be added here
                        alert('Draft functionality not implemented yet');
                      }}
                      className="btn-outline-primary"
                    >
                      <i className="fas fa-save mr-2"></i>
                      Save as Draft
                    </button>
                    
                    <button
                      type="submit"
                      disabled={loading}
                      className="btn-primary"
                    >
                      {loading ? (
                        <>
                          <i className="fas fa-spinner fa-spin mr-2"></i>
                          Creating...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-plus mr-2"></i>
                          Post Job
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          {/* Tips Card */}
          <div className="card mt-8">
            <div className="card-body">
              <h3 className="text-lg font-semibold text-white mb-4">
                <i className="fas fa-lightbulb text-warning mr-2"></i>
                Tips for a Great Job Posting
              </h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-start">
                  <i className="fas fa-check text-success mr-2 mt-1"></i>
                  <span>Write a clear and descriptive job title</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-success mr-2 mt-1"></i>
                  <span>Include specific requirements and qualifications</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-success mr-2 mt-1"></i>
                  <span>Mention company culture and benefits</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-success mr-2 mt-1"></i>
                  <span>Be transparent about salary and compensation</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-success mr-2 mt-1"></i>
                  <span>Set a reasonable application deadline</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
