@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 33, 37, 41;
  --background-end-rgb: 26, 30, 33;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
    to bottom,
    rgb(var(--background-start-rgb)),
    rgb(var(--background-end-rgb))
  );
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary-dark text-white font-medium py-2 px-4 rounded transition-colors;
  }

  .btn-outline-primary {
    @apply border border-primary text-primary hover:bg-primary hover:text-white font-medium py-2 px-4 rounded transition-colors;
  }

  .form-input {
    @apply bg-dark-lighter border border-gray-600 rounded py-2 px-3 text-white w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  .form-select {
    @apply bg-dark-lighter border border-gray-600 rounded py-2 px-3 text-white w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  /* Ensure input text is visible */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  textarea,
  select {
    color: white !important;
    background-color: #343a40 !important;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="search"]:focus,
  textarea:focus,
  select:focus {
    color: white !important;
    background-color: #343a40 !important;
  }

  .form-label {
    @apply block text-sm font-medium mb-1;
  }

  .card {
    @apply bg-dark border border-secondary rounded-lg shadow-card transition-transform hover:translate-y-[-5px];
  }

  .card-body {
    @apply p-4;
  }

  .sidebar {
    @apply fixed top-0 left-0 h-full w-64 bg-dark-darker p-4 transition-all duration-300 ease-in-out;
  }

  .sidebar-collapsed {
    @apply w-16;
  }

  .main-content {
    @apply ml-64 transition-all duration-300 ease-in-out;
  }

  .main-content-sidebar-collapsed {
    @apply ml-16;
  }

  .nav-link {
    @apply flex items-center text-white py-2 px-4 rounded hover:bg-dark-lighter transition-colors;
  }

  .nav-link-active {
    @apply bg-primary text-white;
  }

  .notification-badge {
    @apply absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-danger text-white text-xs rounded-full h-5 w-5 flex items-center justify-center;
  }
}