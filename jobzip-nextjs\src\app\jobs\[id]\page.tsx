'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job } from '@/types';

interface JobDetailPageProps {
  params: { id: string };
}

export default function JobDetailPage({ params }: JobDetailPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState(false);
  const [bookmarking, setBookmarking] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [applicationData, setApplicationData] = useState({
    cover_letter: '',
    resume: '',
  });

  useEffect(() => {
    fetchJob();
  }, [params.id]);

  const fetchJob = async () => {
    try {
      const response = await fetch(`/api/jobs/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setJob(data.job);
      } else if (response.status === 404) {
        router.push('/jobs');
      }
    } catch (error) {
      console.error('Error fetching job:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApply = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session) {
      router.push('/auth/login');
      return;
    }

    setApplying(true);
    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          job_id: parseInt(params.id),
          cover_letter: applicationData.cover_letter,
          resume: applicationData.resume,
        }),
      });

      if (response.ok) {
        setShowApplicationForm(false);
        setApplicationData({ cover_letter: '', resume: '' });
        fetchJob(); // Refresh to update has_applied status
        alert('Application submitted successfully!');
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Failed to submit application');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      alert('An error occurred while submitting your application');
    } finally {
      setApplying(false);
    }
  };

  const handleBookmark = async () => {
    if (!session) {
      router.push('/auth/login');
      return;
    }

    setBookmarking(true);
    try {
      const response = await fetch(`/api/bookmarks/toggle/${params.id}`, {
        method: 'POST',
      });

      if (response.ok) {
        fetchJob(); // Refresh to update bookmark status
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Failed to bookmark job');
      }
    } catch (error) {
      console.error('Error bookmarking job:', error);
      alert('An error occurred while bookmarking');
    } finally {
      setBookmarking(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading job details...</p>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-4xl text-warning mb-4"></i>
          <h1 className="text-2xl font-bold text-white mb-2">Job Not Found</h1>
          <p className="text-gray-400 mb-4">The job you're looking for doesn't exist.</p>
          <Link href="/jobs" className="btn-primary">
            Browse Jobs
          </Link>
        </div>
      </div>
    );
  }

  const isExpired = new Date(job.deadline) < new Date();
  const canApply = session?.user?.userType === 'employee' &&
                   job.status === 'open' &&
                   !isExpired &&
                   !job.has_applied;

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/jobs" className="text-gray-400 hover:text-white">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Jobs
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            {session?.user?.userType === 'employee' && (
              <button
                onClick={handleBookmark}
                disabled={bookmarking}
                className={`btn-outline-primary ${job.is_bookmarked ? 'bg-primary text-white' : ''}`}
              >
                <i className={`fas ${job.is_bookmarked ? 'fa-bookmark' : 'fa-bookmark-o'} mr-2`}></i>
                {job.is_bookmarked ? 'Bookmarked' : 'Bookmark'}
              </button>
            )}
            <Link href={session?.user?.userType === 'employee' ? '/employee/dashboard' : '/employer/dashboard'}
                  className="btn-outline-primary">
              <i className="fas fa-home mr-2"></i>
              Dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-body">
                {/* Job Header */}
                <div className="mb-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h1 className="text-3xl font-bold text-white mb-2">{job.title}</h1>
                      <div className="flex items-center space-x-4 text-gray-400">
                        <span>
                          <i className="fas fa-building mr-2"></i>
                          {job.employer_details?.employer_profile?.company_name || job.company}
                        </span>
                        <span>
                          <i className="fas fa-map-marker-alt mr-2"></i>
                          {job.location}
                        </span>
                        <span>
                          <i className="fas fa-clock mr-2"></i>
                          {job.duration}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`px-3 py-1 rounded-full text-sm ${
                        job.status === 'open' && !isExpired
                          ? 'bg-success/20 text-success'
                          : 'bg-danger/20 text-danger'
                      }`}>
                        {job.status === 'open' && !isExpired ? 'Open' : 'Closed'}
                      </span>
                      {job.salary && (
                        <p className="text-lg font-semibold text-white mt-2">{job.salary}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-400">
                    <span>
                      <i className="fas fa-users mr-2"></i>
                      {job.employees_required} position{job.employees_required > 1 ? 's' : ''} available
                    </span>
                    <span>
                      <i className="fas fa-calendar mr-2"></i>
                      Deadline: {new Date(job.deadline).toLocaleDateString()}
                    </span>
                    <span>
                      <i className="fas fa-file-alt mr-2"></i>
                      {job.applications_count} application{job.applications_count !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {/* Job Description */}
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-white mb-4">Job Description</h2>
                  <div className="prose prose-invert max-w-none">
                    <p className="text-gray-300 whitespace-pre-wrap">{job.description}</p>
                  </div>
                </div>

                {/* Application Status */}
                {session?.user?.userType === 'employee' && (
                  <div className="mb-6">
                    {job.has_applied ? (
                      <div className="bg-info/20 border border-info/30 rounded-lg p-4">
                        <div className="flex items-center">
                          <i className="fas fa-check-circle text-info text-xl mr-3"></i>
                          <div>
                            <h3 className="font-semibold text-white">Application Submitted</h3>
                            <p className="text-gray-400">You have already applied for this position.</p>
                          </div>
                        </div>
                      </div>
                    ) : canApply ? (
                      <div className="space-y-4">
                        {!showApplicationForm ? (
                          <button
                            onClick={() => setShowApplicationForm(true)}
                            className="btn-primary btn-lg w-full"
                          >
                            <i className="fas fa-paper-plane mr-2"></i>
                            Apply for this Job
                          </button>
                        ) : (
                          <div className="bg-dark-lighter rounded-lg p-6">
                            <h3 className="text-xl font-semibold text-white mb-4">Submit Application</h3>
                            <form onSubmit={handleApply} className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                  Cover Letter
                                </label>
                                <textarea
                                  value={applicationData.cover_letter}
                                  onChange={(e) => setApplicationData({
                                    ...applicationData,
                                    cover_letter: e.target.value
                                  })}
                                  rows={6}
                                  className="form-input"
                                  placeholder="Tell us why you're interested in this position..."
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                  Resume/CV (Optional)
                                </label>
                                <textarea
                                  value={applicationData.resume}
                                  onChange={(e) => setApplicationData({
                                    ...applicationData,
                                    resume: e.target.value
                                  })}
                                  rows={4}
                                  className="form-input"
                                  placeholder="Paste your resume content or provide a link..."
                                />
                              </div>
                              <div className="flex space-x-4">
                                <button
                                  type="submit"
                                  disabled={applying}
                                  className="btn-primary flex-1"
                                >
                                  {applying ? (
                                    <>
                                      <i className="fas fa-spinner fa-spin mr-2"></i>
                                      Submitting...
                                    </>
                                  ) : (
                                    <>
                                      <i className="fas fa-paper-plane mr-2"></i>
                                      Submit Application
                                    </>
                                  )}
                                </button>
                                <button
                                  type="button"
                                  onClick={() => setShowApplicationForm(false)}
                                  className="btn-outline-secondary flex-1"
                                >
                                  Cancel
                                </button>
                              </div>
                            </form>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-warning/20 border border-warning/30 rounded-lg p-4">
                        <div className="flex items-center">
                          <i className="fas fa-exclamation-triangle text-warning text-xl mr-3"></i>
                          <div>
                            <h3 className="font-semibold text-white">Cannot Apply</h3>
                            <p className="text-gray-400">
                              {job.status !== 'open' ? 'This job is no longer accepting applications.' :
                               isExpired ? 'The application deadline has passed.' :
                               'You need to be logged in as an employee to apply.'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Company Info */}
            <div className="card">
              <div className="card-body">
                <h3 className="text-lg font-semibold text-white mb-4">About the Company</h3>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-white">
                      {job.employer_details?.employer_profile?.company_name || job.company}
                    </h4>
                    {job.employer_details?.employer_profile?.company_description && (
                      <p className="text-gray-400 text-sm mt-1">
                        {job.employer_details.employer_profile.company_description}
                      </p>
                    )}
                  </div>
                  {job.employer_details?.employer_profile?.location && (
                    <div className="flex items-center text-gray-400 text-sm">
                      <i className="fas fa-map-marker-alt mr-2"></i>
                      {job.employer_details.employer_profile.location}
                    </div>
                  )}
                  {job.employer_details?.employer_profile?.website && (
                    <div className="flex items-center text-gray-400 text-sm">
                      <i className="fas fa-globe mr-2"></i>
                      <a
                        href={job.employer_details.employer_profile.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary-light"
                      >
                        Company Website
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Job Stats */}
            <div className="card">
              <div className="card-body">
                <h3 className="text-lg font-semibold text-white mb-4">Job Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Applications</span>
                    <span className="text-white font-medium">{job.applications_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Positions</span>
                    <span className="text-white font-medium">{job.employees_required}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Posted</span>
                    <span className="text-white font-medium">
                      {new Date(job.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Deadline</span>
                    <span className="text-white font-medium">
                      {new Date(job.deadline).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
