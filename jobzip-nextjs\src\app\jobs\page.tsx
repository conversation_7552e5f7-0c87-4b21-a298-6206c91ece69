'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job } from '@/types';

export default function JobsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    fetchJobs();
  }, [session, status, router, currentPage, searchTerm, locationFilter]);

  const fetchJobs = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        status: 'open',
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      if (locationFilter) {
        params.append('location', locationFilter);
      }

      const response = await fetch(`/api/jobs?${params}`);
      if (response.ok) {
        const data = await response.json();
        setJobs(data.jobs || []);
        setTotalPages(data.pagination?.pages || 1);
      }
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookmark = async (jobId: number) => {
    if (session?.user?.userType !== 'employee') return;

    try {
      const response = await fetch('/api/bookmarks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ job_id: jobId }),
      });

      if (response.ok) {
        // Update the job's bookmark status
        setJobs(jobs.map(job => 
          job.id === jobId ? { ...job, is_bookmarked: true } : job
        ));
      }
    } catch (error) {
      console.error('Error bookmarking job:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchJobs();
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading jobs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Job Listings</h1>
            <p className="text-gray-400">Find your next opportunity</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={session?.user?.userType === 'employee' ? '/employee/dashboard' : '/employer/dashboard'} 
                  className="btn-outline-primary">
              <i className="fas fa-home mr-2"></i>
              Dashboard
            </Link>
            {session?.user?.userType === 'employer' && (
              <Link href="/employer/jobs/new" className="btn-primary">
                <i className="fas fa-plus mr-2"></i>
                Post Job
              </Link>
            )}
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Search and Filters */}
        <div className="card mb-8">
          <div className="card-body">
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search jobs, companies, or keywords..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <div className="md:w-64">
                <input
                  type="text"
                  placeholder="Location"
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-dark-lighter border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <button type="submit" className="btn-primary px-6">
                <i className="fas fa-search mr-2"></i>
                Search
              </button>
            </form>
          </div>
        </div>

        {/* Jobs Grid */}
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-briefcase text-6xl text-gray-600 mb-4"></i>
            <h2 className="text-2xl font-bold text-white mb-2">No jobs found</h2>
            <p className="text-gray-400 mb-6">Try adjusting your search criteria</p>
            {session?.user?.userType === 'employer' && (
              <Link href="/employer/jobs/new" className="btn-primary">
                Post Your First Job
              </Link>
            )}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {jobs.map((job) => (
                <div key={job.id} className="card">
                  <div className="card-body">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-white mb-1">{job.title}</h3>
                        <p className="text-primary font-medium">{job.company}</p>
                      </div>
                      {session?.user?.userType === 'employee' && (
                        <button
                          onClick={() => handleBookmark(job.id)}
                          disabled={job.is_bookmarked}
                          className={`p-2 rounded ${
                            job.is_bookmarked 
                              ? 'text-warning bg-warning/20' 
                              : 'text-gray-400 hover:text-warning hover:bg-warning/20'
                          }`}
                        >
                          <i className={`fas fa-bookmark ${job.is_bookmarked ? '' : 'far'}`}></i>
                        </button>
                      )}
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-400">
                        <i className="fas fa-map-marker-alt mr-2"></i>
                        {job.location}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <i className="fas fa-clock mr-2"></i>
                        {job.duration}
                      </div>
                      {job.salary && (
                        <div className="flex items-center text-sm text-success">
                          <i className="fas fa-dollar-sign mr-2"></i>
                          {job.salary}
                        </div>
                      )}
                      <div className="flex items-center text-sm text-gray-400">
                        <i className="fas fa-users mr-2"></i>
                        {job.employees_required} position{job.employees_required > 1 ? 's' : ''}
                      </div>
                    </div>

                    <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                      {job.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        <div>Posted {new Date(job.created_at).toLocaleDateString()}</div>
                        <div>Deadline: {new Date(job.deadline).toLocaleDateString()}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Link 
                          href={`/jobs/${job.id}`}
                          className="btn-outline-primary text-sm"
                        >
                          View Details
                        </Link>
                        {session?.user?.userType === 'employee' && (
                          <Link 
                            href={`/jobs/${job.id}/apply`}
                            className="btn-primary text-sm"
                          >
                            Apply
                          </Link>
                        )}
                      </div>
                    </div>

                    {job.applications_count !== undefined && (
                      <div className="mt-3 pt-3 border-t border-gray-700">
                        <div className="text-xs text-gray-500">
                          {job.applications_count} application{job.applications_count !== 1 ? 's' : ''}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 bg-dark border border-gray-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-lighter"
                  >
                    Previous
                  </button>
                  
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-2 border border-gray-600 rounded ${
                        currentPage === page
                          ? 'bg-primary text-white'
                          : 'bg-dark text-white hover:bg-dark-lighter'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 bg-dark border border-gray-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-lighter"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
