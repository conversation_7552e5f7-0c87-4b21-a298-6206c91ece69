'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  user_type: string;
  location?: string;
  bio?: string;
  profile_picture?: string;
  date_joined: string;
  last_login?: string;
  employee_profile?: {
    skills?: string;
    experience?: string;
    education?: string;
  };
  employer_profile?: {
    company_name?: string;
    company_description?: string;
    website?: string;
    location?: string;
  };
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    fetchProfile();
  }, [session, status, router]);

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const data = await response.json();
        setProfile(data.user);
        setFormData(data.user);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          location: formData.location,
          bio: formData.bio,
          // Employee fields
          skills: formData.employee_profile?.skills,
          experience: formData.employee_profile?.experience,
          education: formData.employee_profile?.education,
          // Employer fields
          company_name: formData.employer_profile?.company_name,
          company_description: formData.employer_profile?.company_description,
          website: formData.employer_profile?.website,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data.user);
        setFormData(data.user);
        setEditing(false);
        alert('Profile updated successfully!');
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('An error occurred while updating your profile');
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData((prev: any) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData((prev: any) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-4xl text-warning mb-4"></i>
          <h1 className="text-2xl font-bold text-white mb-2">Profile Not Found</h1>
          <p className="text-gray-400 mb-4">Unable to load your profile.</p>
          <Link href="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Profile</h1>
            <p className="text-gray-400">Manage your account information</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link 
              href={session?.user?.userType === 'employee' ? '/employee/dashboard' : '/employer/dashboard'} 
              className="btn-outline-primary"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Summary */}
            <div className="lg:col-span-1">
              <div className="card">
                <div className="card-body text-center">
                  <div className="mb-4">
                    <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i className="fas fa-user text-3xl text-primary"></i>
                    </div>
                    <h2 className="text-xl font-bold text-white">
                      {profile.first_name || profile.last_name 
                        ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
                        : profile.username}
                    </h2>
                    <p className="text-gray-400">@{profile.username}</p>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm mt-2 ${
                      profile.user_type === 'employee' 
                        ? 'bg-success/20 text-success' 
                        : 'bg-primary/20 text-primary'
                    }`}>
                      {profile.user_type === 'employee' ? 'Job Seeker' : 'Employer'}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm text-gray-400">
                    <div className="flex items-center justify-center">
                      <i className="fas fa-envelope mr-2"></i>
                      {profile.email}
                    </div>
                    {profile.location && (
                      <div className="flex items-center justify-center">
                        <i className="fas fa-map-marker-alt mr-2"></i>
                        {profile.location}
                      </div>
                    )}
                    <div className="flex items-center justify-center">
                      <i className="fas fa-calendar mr-2"></i>
                      Joined {new Date(profile.date_joined).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Details */}
            <div className="lg:col-span-2">
              <div className="card">
                <div className="card-body">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-white">Profile Information</h3>
                    {!editing ? (
                      <button
                        onClick={() => setEditing(true)}
                        className="btn-outline-primary"
                      >
                        <i className="fas fa-edit mr-2"></i>
                        Edit Profile
                      </button>
                    ) : (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setEditing(false);
                            setFormData(profile);
                          }}
                          className="btn-outline-secondary"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSave}
                          disabled={saving}
                          className="btn-primary"
                        >
                          {saving ? (
                            <>
                              <i className="fas fa-spinner fa-spin mr-2"></i>
                              Saving...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-save mr-2"></i>
                              Save Changes
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div>
                      <h4 className="text-lg font-medium text-white mb-4">Basic Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            First Name
                          </label>
                          {editing ? (
                            <input
                              type="text"
                              name="first_name"
                              value={formData.first_name || ''}
                              onChange={handleChange}
                              className="form-input"
                            />
                          ) : (
                            <p className="text-gray-400">{profile.first_name || 'Not provided'}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Last Name
                          </label>
                          {editing ? (
                            <input
                              type="text"
                              name="last_name"
                              value={formData.last_name || ''}
                              onChange={handleChange}
                              className="form-input"
                            />
                          ) : (
                            <p className="text-gray-400">{profile.last_name || 'Not provided'}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Email
                          </label>
                          {editing ? (
                            <input
                              type="email"
                              name="email"
                              value={formData.email || ''}
                              onChange={handleChange}
                              className="form-input"
                            />
                          ) : (
                            <p className="text-gray-400">{profile.email}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Location
                          </label>
                          {editing ? (
                            <input
                              type="text"
                              name="location"
                              value={formData.location || ''}
                              onChange={handleChange}
                              className="form-input"
                            />
                          ) : (
                            <p className="text-gray-400">{profile.location || 'Not provided'}</p>
                          )}
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Bio
                        </label>
                        {editing ? (
                          <textarea
                            name="bio"
                            value={formData.bio || ''}
                            onChange={handleChange}
                            rows={3}
                            className="form-input"
                            placeholder="Tell us about yourself..."
                          />
                        ) : (
                          <p className="text-gray-400">{profile.bio || 'No bio provided'}</p>
                        )}
                      </div>
                    </div>

                    {/* Employee-specific fields */}
                    {profile.user_type === 'employee' && (
                      <div>
                        <h4 className="text-lg font-medium text-white mb-4">Professional Information</h4>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Skills
                            </label>
                            {editing ? (
                              <textarea
                                name="employee_profile.skills"
                                value={formData.employee_profile?.skills || ''}
                                onChange={handleChange}
                                rows={3}
                                className="form-input"
                                placeholder="List your skills and technologies..."
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employee_profile?.skills || 'No skills listed'}
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Experience
                            </label>
                            {editing ? (
                              <textarea
                                name="employee_profile.experience"
                                value={formData.employee_profile?.experience || ''}
                                onChange={handleChange}
                                rows={4}
                                className="form-input"
                                placeholder="Describe your work experience..."
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employee_profile?.experience || 'No experience listed'}
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Education
                            </label>
                            {editing ? (
                              <textarea
                                name="employee_profile.education"
                                value={formData.employee_profile?.education || ''}
                                onChange={handleChange}
                                rows={3}
                                className="form-input"
                                placeholder="List your educational background..."
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employee_profile?.education || 'No education listed'}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Employer-specific fields */}
                    {profile.user_type === 'employer' && (
                      <div>
                        <h4 className="text-lg font-medium text-white mb-4">Company Information</h4>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Company Name
                            </label>
                            {editing ? (
                              <input
                                type="text"
                                name="employer_profile.company_name"
                                value={formData.employer_profile?.company_name || ''}
                                onChange={handleChange}
                                className="form-input"
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employer_profile?.company_name || 'Not provided'}
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Company Description
                            </label>
                            {editing ? (
                              <textarea
                                name="employer_profile.company_description"
                                value={formData.employer_profile?.company_description || ''}
                                onChange={handleChange}
                                rows={4}
                                className="form-input"
                                placeholder="Describe your company..."
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employer_profile?.company_description || 'No description provided'}
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                              Website
                            </label>
                            {editing ? (
                              <input
                                type="url"
                                name="employer_profile.website"
                                value={formData.employer_profile?.website || ''}
                                onChange={handleChange}
                                className="form-input"
                                placeholder="https://..."
                              />
                            ) : (
                              <p className="text-gray-400">
                                {profile.employer_profile?.website ? (
                                  <a 
                                    href={profile.employer_profile.website}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-primary hover:text-primary-light"
                                  >
                                    {profile.employer_profile.website}
                                  </a>
                                ) : (
                                  'No website provided'
                                )}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
