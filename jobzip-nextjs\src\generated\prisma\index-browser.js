
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  first_name: 'first_name',
  last_name: 'last_name',
  password: 'password',
  user_type: 'user_type',
  profile_picture: 'profile_picture',
  location: 'location',
  bio: 'bio',
  date_joined: 'date_joined',
  is_active: 'is_active',
  is_staff: 'is_staff',
  is_superuser: 'is_superuser',
  last_login: 'last_login'
};

exports.Prisma.JobScalarFieldEnum = {
  id: 'id',
  employer_id: 'employer_id',
  title: 'title',
  description: 'description',
  location: 'location',
  duration: 'duration',
  company: 'company',
  salary: 'salary',
  employees_required: 'employees_required',
  deadline: 'deadline',
  created_at: 'created_at',
  status: 'status'
};

exports.Prisma.CompanyPictureScalarFieldEnum = {
  id: 'id',
  image: 'image',
  uploaded_at: 'uploaded_at',
  job_id: 'job_id'
};

exports.Prisma.JobApplicationScalarFieldEnum = {
  id: 'id',
  job_id: 'job_id',
  applicant_id: 'applicant_id',
  status: 'status',
  cover_letter: 'cover_letter',
  resume: 'resume',
  applied_at: 'applied_at',
  updated_at: 'updated_at'
};

exports.Prisma.JobEnrollmentScalarFieldEnum = {
  id: 'id',
  job_id: 'job_id',
  employee_id: 'employee_id',
  status: 'status',
  enrolled_at: 'enrolled_at'
};

exports.Prisma.JobReviewScalarFieldEnum = {
  id: 'id',
  job_id: 'job_id',
  employee_id: 'employee_id',
  rating: 'rating',
  comment: 'comment',
  created_at: 'created_at'
};

exports.Prisma.EmployeeReviewScalarFieldEnum = {
  id: 'id',
  job_id: 'job_id',
  employee_id: 'employee_id',
  employer_id: 'employer_id',
  rating: 'rating',
  remarks: 'remarks',
  created_at: 'created_at'
};

exports.Prisma.BookmarkScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  job_id: 'job_id',
  created_at: 'created_at'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  notification_type: 'notification_type',
  message: 'message',
  is_read: 'is_read',
  created_at: 'created_at'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  reporter_id: 'reporter_id',
  reported_user_id: 'reported_user_id',
  reason: 'reason',
  created_at: 'created_at',
  status: 'status',
  ban_duration: 'ban_duration'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  review_id: 'review_id',
  user_id: 'user_id',
  content: 'content',
  parent_id: 'parent_id',
  created_at: 'created_at'
};

exports.Prisma.EmployerProfileScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  company_name: 'company_name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.EmployeeProfileScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  location: 'location',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  Job: 'Job',
  CompanyPicture: 'CompanyPicture',
  JobApplication: 'JobApplication',
  JobEnrollment: 'JobEnrollment',
  JobReview: 'JobReview',
  EmployeeReview: 'EmployeeReview',
  Bookmark: 'Bookmark',
  Notification: 'Notification',
  Report: 'Report',
  Comment: 'Comment',
  EmployerProfile: 'EmployerProfile',
  EmployeeProfile: 'EmployeeProfile'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
