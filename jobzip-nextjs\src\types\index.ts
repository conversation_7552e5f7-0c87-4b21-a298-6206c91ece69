// NextAuth types extension
import 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    username: string;
    email: string;
    userType: 'employee' | 'employer';
  }

  interface Session {
    user: User;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    userType: 'employee' | 'employer';
  }
}

// Application types - Updated to match Django models exactly
export interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  user_type: 'employee' | 'employer';
  profile_picture?: string;
  location?: string;
  bio?: string;
  date_joined: string;
  last_login?: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  // Profile relationships
  employer_profile?: EmployerProfile;
  employee_profile?: EmployeeProfile;
}

export interface Job {
  id: number;
  employer_id: number; // User ID
  title: string;
  description: string;
  location: string;
  duration: string;
  company: string;
  salary?: string;
  employees_required: number;
  deadline: string;
  created_at: string;
  status: 'open' | 'closed';
  company_pictures?: CompanyPicture[];
  // Computed fields
  employer_details?: User;
  applications_count?: number;
  is_bookmarked?: boolean;
  has_applied?: boolean;
}

export interface CompanyPicture {
  id: number;
  image: string;
  uploaded_at: string;
}

export interface JobApplication {
  id: number;
  job_id: number; // Job ID
  applicant_id: number; // User ID
  status: 'pending' | 'accepted' | 'rejected';
  cover_letter?: string;
  resume?: string;
  applied_at: string;
  updated_at: string;
  // Populated fields
  job_details?: Job;
  applicant_details?: User;
}

export interface JobEnrollment {
  id: number;
  job: number; // Job ID
  employee: number; // User ID
  status: string;
  enrolled_at: string;
}

export interface JobReview {
  id: number;
  job: number; // Job ID
  employee: number; // User ID
  rating: number;
  comment: string;
  created_at: string;
  // Populated fields
  job_details?: Job;
  employee_details?: User;
}

export interface EmployeeReview {
  id: number;
  job: number; // Job ID
  employee: number; // User ID
  employer: number; // User ID
  rating: number;
  remarks: string;
  created_at: string;
  // Populated fields
  job_details?: Job;
  employee_details?: User;
  employer_details?: User;
}

export interface Bookmark {
  id: number;
  user: number; // User ID
  job: number; // Job ID
  created_at: string;
  // Populated fields
  job_details?: Job;
}

export interface Notification {
  id: number;
  user_id: number; // User ID
  notification_type: 'listing' | 'bookmark' | 'review' | 'current_job' | 'general' | 'application';
  message: string;
  is_read: boolean;
  created_at: string;
}

export interface Report {
  id: number;
  reporter: number; // User ID
  reported_user: number; // User ID
  reason: string;
  created_at: string;
  status: string;
  ban_duration?: number; // Duration in days
  // Populated fields
  reporter_details?: User;
  reported_user_details?: User;
}

export interface Comment {
  id: number;
  review: number; // JobReview ID
  user: number; // User ID
  content: string;
  parent?: number; // Comment ID for replies
  created_at: string;
  likes: number[]; // User IDs
  dislikes: number[]; // User IDs
  // Populated fields
  user_details?: User;
  replies?: Comment[];
}

export interface EmployerProfile {
  id: number;
  user_id: number; // User ID
  company_name?: string;
  company_description?: string;
  website?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface EmployeeProfile {
  id: number;
  user_id: number; // User ID
  skills?: string;
  experience?: string;
  education?: string;
  created_at: string;
  updated_at: string;
}